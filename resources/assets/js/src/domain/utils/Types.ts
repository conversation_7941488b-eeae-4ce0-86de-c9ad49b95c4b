type Timezone = string;
type Date = string;
type DateTime = string;

/**
 * utility type to define extendable objects
 * for this:

 type FooBar = Schema<{
			foo: string,
			bar: string
	}>;

	const correct: FooBar = {
			foo: 'foo',
			bar: 'bar',
			whatever: 123,
			whatever_else: [1, 'a']
	};

	const missing_mandatory_prop: FooBar = {
			foo: 'foo',
			whatever: 123,
			whatever_else: [1, 'a']
	};

	const bad_prop_type: FooBar = {
			foo: 'foo',
			bar: 123,
			whatever: 123,
			whatever_else: [1, 'a']
	};
 */
type Schema<T> = T & Record<string, unknown>;

/**
 * utility type to controller hooks
 * for example:

 import { onMounted } from 'vue';
 import {Hook} from "@/domain/utils/Types";

const myControllerFactory =
	({ onMountedHook } : { onMountedHook: Hook }) =>
		({ props }): View => {

			onMountedHook( () => {
				// this function is expected to have signature
				// that was passed to Hook<signatureType>
			});

			return { }
		}

 const myController = (props: Props, ctx: SetupContext) => myControllerFactory({
			onMountedHook: onMounted as Hook
		})({ props });
*/
type Hook = <T = () => void, R = void>(callback: T, target?: unknown) => R;

type Collection<T> = T[];

type Collectable<T> = Record<string | number, T>;

type Callable = (...args: never[]) => void;

export { Date, Timezone, DateTime, Hook, Schema, Collection, Collectable, Callable };
