import { provide } from 'vue';

const withLinks = (descriptor, provideMe = false) => ({
	...descriptor,

	props: {
		links: {
			type: Object,
			required: true,
		},
		...(descriptor.props || {}),
	},

	setup: (props, ...args) => {
		const applicationLinks = {
			links: props.links,
			get: (link) => props.links[link],
		};

		if (provideMe) {
			provide('applicationLinks', applicationLinks);
		}

		return {
			...(descriptor.setup || (() => {}))(props, ...args),
			applicationLinks,
		};
	},
});

const withLinksProvide = (...args) => withLinks(...args, true);

export { withLinks, withLinksProvide };
