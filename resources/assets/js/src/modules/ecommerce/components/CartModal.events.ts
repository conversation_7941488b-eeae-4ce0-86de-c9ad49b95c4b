import { emit, on, EventCallback as SignalCallback } from '@/domain/services/LocalEventBus';

enum CartModalEvents {
	Open = 'cart-modal:open',
}

type OpenModalPayload = {
	cartRoute: string;
	title: string;
};

type CartModalEventPayloads = {
	[CartModalEvents.Open]: OpenModalPayload;
};

const cartBus = {
	emit: <T extends CartModalEvents>(signal: T, payload: CartModalEventPayloads[T]) =>
		emit<CartModalEventPayloads, T>(signal, payload),

	on: <T extends CartModalEvents>(signal: T, callback: SignalCallback<CartModalEventPayloads[T]>) =>
		on<CartModalEventPayloads, T>(signal, callback),
};

export { cartBus, CartModalEvents };
