<template>
	<div>
		<div v-if="formSelectionEnabled" id="formMultiselect" class="panel panel-default panel-body">
			<div class="panel-title">
				<h4>{{ title() }}</h4>
			</div>

			<div class="radio styled">
				<input id="formOptionAll" v-model="formOption" name="formOption" type="radio" value="all" />
				<label for="formOptionAll">
					<span class="mbs">
						{{ formsAll() }}
					</span>
				</label>
			</div>

			<div class="radio styled">
				<input id="formOptionSelect" v-model="formOption" name="formOption" type="radio" value="select" />
				<label for="formOptionSelect">
					<span class="mbs">
						{{ formsSelect() }}
					</span>
				</label>
			</div>

			<div v-if="!allFormsSelected" id="formSelection">
				<div class="form-group">
					<multiselect
						id="form-selector"
						name="forms[]"
						id-property="slug"
						:options="filteredForms"
						:selected-options="selectedForms"
						:placeholder="placeholder()"
						:select-all-label="selectAll()"
						@selected="toggleSelectedForms"
					></multiselect>
				</div>
			</div>
		</div>

		<category-multiselect
			v-if="categorySelectionEnabled"
			:selected-category-option="selectedCategoryOption"
			:starting-categories="categories"
			:selected-categories="selectedCategories"
			:starting-form="startingForm"
			:selected-form="selectedForm"
		></category-multiselect>

		<score-set-multiselect
			v-if="scoreSetSelectionEnabled"
			:selected-score-set-option="selectedScoreSetOption"
			:starting-score-sets="scoreSets"
			:selected-score-sets="selectedScoreSets"
		></score-set-multiselect>

		<recipient-options
			v-if="recipientOptionsEnabled"
			:trigger="trigger"
			:selected-recipient-option="selectedRecipientOption"
			:starting-recipients="recipients"
			:starting-fields="fields"
			:selected-field="selectedField"
			:starting-form="startingForm"
			:selected-form="selectedForm"
			:one-form-selected="oneFormSelected"
			:notification-settings="notificationSettings"
		></recipient-options>
	</div>
</template>

<script lang="ts">
import { formMultiselectController, FormMultiselectProps } from './FormMultiselect.controller';
import { defineComponent, PropType } from 'vue';
import CategoryMultiselect from '@/modules/notifications/components/CategoryMultiselect.vue';
import ScoreSetMultiselect from '@/modules/notifications/components/ScoreSetMultiselect.vue';
import RecipientOptions from '@/modules/notifications/components/RecipientOptions.vue';
import { Multiselect } from 'vue-bootstrap';
import { Settings } from '@/modules/notifications/components/Notification.types';

export default defineComponent({
	components: {
		Multiselect,
		CategoryMultiselect,
		ScoreSetMultiselect,
		RecipientOptions,
	},
	props: {
		trigger: {
			type: String,
			required: true,
		},
		isMultiform: {
			type: Boolean,
			required: true,
		},
		selectedFormOption: {
			type: String,
			required: true,
		},
		forms: {
			type: Array,
			required: true,
		},
		selectedForms: {
			type: Array,
			required: true,
		},
		selectedCategoryOption: {
			type: String,
			required: true,
		},
		categories: {
			type: Array,
			required: true,
		},
		selectedCategories: {
			type: Array,
			required: true,
		},
		selectedScoreSetOption: {
			type: String,
			required: true,
		},
		scoreSets: {
			type: Array,
			required: true,
		},
		selectedScoreSets: {
			type: Array,
			required: true,
		},
		selectedRecipientOption: {
			type: String,
			required: true,
		},
		recipients: {
			type: String,
			required: true,
		},
		fields: {
			type: Array,
			required: true,
		},
		selectedField: {
			type: String,
			required: true,
		},
		notificationSettings: {
			type: Object as PropType<Settings>,
			required: true,
		},
	},
	setup(props: FormMultiselectProps) {
		return formMultiselectController(props);
	},
});
</script>
