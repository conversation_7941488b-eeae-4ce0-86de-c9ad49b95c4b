<template>
	<div>
		<div class="panel-title">
			<h4>{{ lang.get('billing.account_contacts.billing_contacts.title') }}</h4>
		</div>
		<table class="table contacts-table">
			<colgroup>
				<col span="3" class="contacts-table-columns" />
			</colgroup>
			<thead>
				<tr>
					<th>{{ lang.get('billing.account_contacts.billing_contacts.table.first_name') }}</th>
					<th>{{ lang.get('billing.account_contacts.billing_contacts.table.last_name') }}</th>
					<th>{{ lang.get('billing.account_contacts.billing_contacts.table.email') }}</th>
					<th class="overflow-cell"></th>
				</tr>
			</thead>
			<contacts-billing-contacts-row
				v-for="billingContact in localBillingContacts"
				:key="billingContact.id"
				:billing-contact="billingContact"
				:can-edit="isOwner"
				:customer-id="customerId"
				@contactDeleted="removeDeletedContact"
			></contacts-billing-contacts-row>
			<contacts-billing-contacts-row
				v-if="showNewContactForm"
				:can-edit="false"
				:customer-id="customerId"
				:view-mode="false"
				@contactSaved="addNewContact"
				@formClosed="toggleAddNewContactForm"
			></contacts-billing-contacts-row>
			<tbody class="add-contact-row">
				<tr v-if="isOwner && !showNewContactForm">
					<td>
						<button class="btn btn-sm btn-secondary" @click="toggleAddNewContactForm">
							{{ lang.get('billing.account_contacts.buttons.add_contact') }}
						</button>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import ContactsBillingContactsRow from './ContactsBillingContactsRow';
import { BillingContact } from '@/modules/billing/Billing.types';
import { useController } from '@/domain/services/Composer';
import {
	contactsBillingContactsController,
	Props,
	View,
} from '@/modules/billing/components/contacts/ContactsBillingContacts.controller';
export default defineComponent<Props, View>({
	components: {
		ContactsBillingContactsRow,
	},

	props: {
		billingContacts: {
			type: Array as PropType<BillingContact[]>,
			required: true,
		},
		isOwner: {
			type: Boolean,
			default: false,
		},
		customerId: {
			type: String,
			required: true,
		},
	},

	setup: useController(contactsBillingContactsController, 'ContactsBillingContactsController') as () => View,
});
</script>

<style scoped>
.contacts-table {
	margin: unset !important;
}
.contacts-table-columns {
	width: 30% !important;
}
.add-contact-row,
.add-contact-row td {
	border: unset !important;
}
</style>
