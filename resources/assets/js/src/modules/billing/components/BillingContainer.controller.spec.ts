import { TabId } from '@/modules/billing/Billing.types';
import { useContainer } from '@/domain/services/Container';
import { beforeEach, describe, expect, Mock, vi } from 'vitest';
import { billingContainerController, View } from '@/modules/billing/components/BillingContainer.controller';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		language: {
			locale: 'en_GB',
			fallback: 'en_GB',
		},
		variables: {
			labels: {
				contacts: 'Account contacts',
				invoices: 'Invoices',
				subscriptions: 'Subscription',
			},
			billingData: {
				accountOwner: {},
				billingContacts: [],
				countries: {},
				invoices: [],
				organisation: {},
				subscription: {},
				vatCountries: {},
			},
		},
	},
}));

const defaultProps = {
	labels: {
		contacts: 'Account contacts',
		invoices: 'Invoices',
		subscriptions: 'Subscription',
	},
	billingData: {
		accountOwner: {},
		billingContacts: [],
		countries: {},
		invoices: [],
		organisation: {},
		subscription: {},
		vatCountries: {},
	},
};

vi.mock('@/domain/services/Container', () => ({
	useContainer: vi.fn(),
}));

describe('BillingContainer controller', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		vi.clearAllMocks();
		(useContainer as Mock).mockReturnValue({ onMounted: vi.fn() });
	});

	it('should not show invoices tab if consumer is not account owner', () => {
		const view = billingContainerController(defaultProps) as View;

		expect(view.tabs.value.length).toBe(2);
		expect(view.tabs.value.find((tab) => tab.id === TabId.INVOICES)).toBeUndefined();
	});

	it('should show invoices tab only if consumer is account owner', () => {
		const props = {
			...defaultProps,
			isOwner: true,
		};
		const view = billingContainerController(props) as View;

		expect(view.tabs.value.length).toBe(3);
		expect(view.tabs.value.find((tab) => tab.id === TabId.INVOICES)).toBeDefined();
	});
});
