<template>
  <div class="score-set-stats mts mbs">
    <score-set-stat
      v-if="['vip_judging', 'qualifying', 'top_pick', 'voting'].includes(scoreSet.mode)"
      :count="stats.assignmentCount"
      :label="stats.entriesAssignedCountLabel"
    />

    <score-set-stat-fraction
      v-if="scoreSet.mode === 'voting'"
      :numerator="stats.votesCount"
      :denominator="stats.maxVotesCount"
      :allow-hidden-denominator="true"
      :label="stats.votesCountLabel"
    />

    <score-set-stat-fraction
      v-if="scoreSet.mode === 'top_pick'"
      :numerator="stats.picksCount"
      :denominator="stats.potentialPicksCount"
      :label="stats.picksCountLabel"
    />
  </div>
</template>

<script>
import ScoreSetStat from './ScoreSetStat';
import ScoreSetStatFraction from './ScoreSetStatFraction';

export default {
  inject: ['lang'],
  components: {
    ScoreSetStat,
    ScoreSetStatFraction
  },
  props: {
    scoreSet: {
      type: Object,
      required: true
    },
    stats: {
      type: Object,
      required: true
    },
    routes: {
      type: Object,
      required: true
    }
  }
};
</script>
