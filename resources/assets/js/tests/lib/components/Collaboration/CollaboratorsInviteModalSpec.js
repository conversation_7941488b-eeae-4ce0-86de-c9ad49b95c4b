import { CollaboratorPrivilege } from '@/domain/models/Collaborator';
import CollaboratorsInviteModal from '@/lib/components/Collaboration/CollaboratorsInviteModal.vue';
import { Composer } from '@/domain/services/Composer';
import { expect } from 'chai';
import { shallowMount } from '@vue/test-utils';

const defaultOptions = {
	lang: { get: (t) => t },
	visible: true,
	loading: false,
	ready: false,
	isModalOpen: true,
	onClose: () => {},
	form: {
		email: '',
		role: 'editor',
		privilege: CollaboratorPrivilege.VIEWER,
	},
	formCallbacks: {
		onEmails: () => {},
		onPrivilege: () => {},
	},
	onInvite: () => {},
	privileges: [],
};
describe('Collaborators invite modal component', () => {
	beforeEach(() => {
		Composer.mockView('collaboratorsInviteModalController', defaultOptions);
	});

	it('it should show the modal', () => {
		const snapshot = shallowMount(CollaboratorsInviteModal);

		const form = snapshot.find('form[class="collaborators-form"]');
		expect(form.exists()).to.be.true;
	});

	it('it should not show the modal', () => {
		Composer.mockView('collaboratorsInviteModalController', Object.assign(defaultOptions, { visible: false }));

		const snapshot = shallowMount(CollaboratorsInviteModal);
		const form = snapshot.find('form[class="collaborators-form"]');

		expect(form.exists()).to.be.false;
	});

	it('it should show the email box component', () => {
		Composer.mockView('collaboratorsInviteModalController', Object.assign(defaultOptions, { visible: true }));

		const snapshot = shallowMount(CollaboratorsInviteModal);
		const emailBox = snapshot.find('emails-box-stub');

		expect(emailBox.exists()).to.be.true;
	});

	it('it should show the select field component', () => {
		Composer.mockView('collaboratorsInviteModalController', Object.assign(defaultOptions, { visible: true }));

		const snapshot = shallowMount(CollaboratorsInviteModal);
		const selectField = snapshot.find('select-field-stub');

		expect(selectField.exists()).to.be.true;
	});

	it('it should show the markdown editor component', () => {
		Composer.mockView('collaboratorsInviteModalController', Object.assign(defaultOptions, { visible: true }));

		const snapshot = shallowMount(CollaboratorsInviteModal);
		const textEditor = snapshot.find('text-editor-stub');

		expect(textEditor.exists()).to.be.true;
	});
});
