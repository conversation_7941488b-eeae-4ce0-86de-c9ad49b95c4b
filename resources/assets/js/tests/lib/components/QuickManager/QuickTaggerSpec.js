import PortalVue from 'portal-vue';
import { shallowMount, createLocalVue } from '@vue/test-utils';
import { expect } from  'chai';
import sinon from 'sinon';
import QuickTagger from '../../../../src/lib/components/QuickManager/QuickTagger.vue';

const localVue = createLocalVue();
localVue.use(PortalVue);

describe('QuickTagger', () => {
  it('sets current tags', () => {
    const quickTagger = shallowMount(QuickTagger, {
      propsData: {
        tags: 'tag A, tag B',
        labels: {},
        url: 'https://af.test'
      },
      localVue
    });

    expect(quickTagger.vm.currentTags).to.equal('tag A, tag B');
  });

  it('watches and saves tags', async () => {
    const response = Promise.resolve();
    const urlSpy = sinon.spy();
    const tagsSpy = sinon.spy();
    const quickTagger = shallowMount(QuickTagger, {
      propsData: {
        url: 'https://af.test/tag',
        tags: [],
        labels: {}
      },
      mocks: {
        $http: {
          post: (url, params) => {
            urlSpy(url);
            tagsSpy(params.tags);

            return response;
          }
        }
      },
      localVue
    });

    await quickTagger.setData({
      currentTags: ['tag A', 'tag B', 'tag C']
    });

    return response.then(() => {
      expect(urlSpy.withArgs('https://af.test/tag').calledOnce).to.be.true;
      expect(tagsSpy.withArgs(['tag A', 'tag B', 'tag C']).calledOnce).to.be.true;
    });
  });
});
