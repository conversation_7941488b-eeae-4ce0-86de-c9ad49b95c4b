import Allocations from '@/modules/funding/components/allocation/Allocations';
import Vuex from 'vuex';
import {createLocalVue, shallowMount} from '@vue/test-utils';
import getters from '@/lib/store/modules/entry-form/getters';
import mutations from '@/lib/store/modules/entry-form/mutations';
import sinon from 'sinon';
import {expect} from 'chai';

const lang = {get: () => ''};

const funds = {
	'1': {
		name: 'Fund 2022',
		available: '€ 96,585.00',
		label: 'Fund 2022 (EUR € 96,585.00 available)',
		currency: 'EUR'
	},
	'2': {
		name: 'Fund 2023',
		available: '€ 99,900.00',
		label: 'Fund 2023 (EUR € 99,900.00 available)',
		currency: 'EUR'
	},
	'5': {
		name: 'Fund test decimal places',
		available: 'د.إ 88,888.00',
		label: 'Fund test decimal places (AED د.إ 88,888.00 available)',
		currency: 'AED'
	}
};

const defaultSuccessResponse = (options = {}) => ({
	data: {
		...{
			totals: 'EUR&nbsp;€ 2,221.00',
			paid: 'EUR&nbsp;€ 223.00',
			funds: funds
		},
		...options
	}
});

const defaultFunding = options => ({
	allocations: [
		{
			id: 138,
			slug: 'RPwWAgvN',
			fundId: 1,
			entryId: 4033,
			currency: 'EUR',
			name: 'Fund 2022',
			amount: '€ 987.00',
			rawAmount: 987,
			tags: [],
			paid: '€ 223.00',
			created_at: '2023-04-03',
			grantEndDate: null,
			allocationPayments: [
				{
					id: 381,
					accountId: 1,
					slug: 'BRDorlKW',
					reference: 'sdfdf',
					paymentMethodId: 14,
					allocationId: 138,
					dateDue: '2023-04-03',
					datePaid: '2023-04-28',
					status: 'paid',
					amount: '223',
					currency: 'EUR',
					createdAt: '2023-04-05T08:12:59.000000Z',
					updatedAt: '2023-04-05T09:42:45.000000Z',
					deletedAt: null,
					seasonId: 6,
					entryId: 4033,
					fundId: 1,
					comments: '[]',
					formattedAmount: '€ 223.00'
				},
				{
					id: 386,
					accountId: 1,
					slug: 'kELzkngR',
					reference: null,
					paymentMethodId: 16,
					allocationId: 138,
					dateDue: null,
					datePaid: null,
					status: 'scheduled',
					amount: '55',
					currency: 'EUR',
					createdAt: '2023-04-13T20:59:04.000000Z',
					updatedAt: '2023-04-13T20:59:04.000000Z',
					deletedAt: null,
					seasonId: 6,
					entryId: 4033,
					fundId: 1,
					comments: '[]',
					formattedAmount: '€ 55.00'
				}
			]
		},
		{
			id: 140,
			slug: 'MgXXrNNN',
			fundId: 1,
			entryId: 4033,
			currency: 'EUR',
			name: 'Fund 2022',
			amount: '€ 1,234.00',
			rawAmount: 1234,
			tags: [],
			paid: '€ 0.00',
			created_at: '2023-04-14',
			grantEndDate: null,
			allocationPayments: []
		}
	],
	totals: 'EUR&nbsp;€ 2,221.00',
	funds: funds,
	paid: 'EUR&nbsp;€ 223.00',
	messages: {
		error:
			"Oops! Something didn't go right. The technical team have been notified and will investigate. If the problem persists, try again later."
	},
	routes: {
		index: 'https://test.site/funding/allocation/entries/kYrpXmxb',
		add: 'https://test.site/funding/allocation/kYrpXmxb',
		update: 'https://test.site/funding/allocation/kYrpXmxb',
		show: 'funding/allocation/kYrpXmxb/{fundAllocation}',
		delete: 'https://test.site/funding/allocation/kYrpXmxb'
	},
	...options
});

const localVue = createLocalVue();
localVue.use(Vuex);

const defaultStore = options =>
	new Vuex.Store({
		modules: {
			allocationPayments: {
				namespaced: true,
				state: {
					allocation: null,
					editOnlyAllocationPayment: null,
					modalIsOpen: false
				},
				getters: getters,
				mutations: mutations
			}
		},
		...options
	});

const defaultAllocations = (options = {}) => ({
	provide: {lang},
	propsData: {
		allocationPermissions: {
			canViewPayments: true,
			canUpdateOrDelete: true,
			canCreate: true,
			canUpdate: true,
			canDelete: true
		},
		locale: 'en_GB',
		originalFunding: defaultFunding()
	},
	methods: {
		getFunds() {
			return [];
		}
	},
	localVue,
	store: defaultStore(),
	...options
});

describe('Allocations', () => {
	it('opens modal if is closed', () => {
		const closeModalSpy = sinon.spy();
		const allocations = shallowMount(Allocations, defaultAllocations());

		allocations.setMethods({
			closeModal: closeModalSpy
		});

		allocations.vm.$options.watch.modalIsOpen.call(allocations.vm, false, true);
		expect(closeModalSpy.calledOnce).to.be.true;
	});

	it('adds an allocation', () => {
		const allocations = shallowMount(Allocations, defaultAllocations());

		allocations.setData({
			allocations: []
		});

		expect(allocations.vm.allocations.length).to.equal(0);
		allocations.vm.addAllocation({
			id: 999,
			slug: 'ABCDEF',
			fundId: 1,
			entryId: 4033,
			currency: 'EUR',
			name: 'Fund 2023',
			amount: '€ 987.00',
			rawAmount: 987,
			tags: [],
			paid: '€ 0.00',
			created_at: '2023-04-03',
			grantEndDate: null
		});

		expect(allocations.vm.allocations.length).to.equal(1);
	});

	it('deletes an allocation', done => {
		const allocations = shallowMount(
			Allocations,
			defaultAllocations({
				mocks: {
					$http: {
						delete: () => Promise.resolve(defaultSuccessResponse())
					}
				}
			})
		);

		allocations.setData({
			allocations: []
		});

		allocations.vm.addAllocation({
			id: 999,
			slug: 'ABCDEF',
			fundId: 1,
			entryId: 4033,
			currency: 'EUR',
			name: 'Fund 2023',
			amount: '€ 987.00',
			rawAmount: 987,
			tags: [],
			paid: '€ 0.00',
			created_at: '2023-04-03',
			grantEndDate: null
		});

		expect(allocations.vm.allocations.length).to.equal(1);

		allocations.setData({
			allocationToBeDeletedIndex: 0
		});

		allocations.vm.deleteAllocation();

		setTimeout(() => {
			expect(allocations.vm.modeState.isEmptyMode()).to.be.true;
			expect(allocations.vm.allocations.length).to.equal(0);
			done();
		}, 200);
	});

	it('displays delete modal when allocation delete is clicked', done => {
		const cancelAllocationSpy = sinon.spy();
		const allocations = shallowMount(
			Allocations,
			defaultAllocations({
				mocks: {
					$http: {
						delete: () => Promise.resolve(defaultSuccessResponse())
					}
				}
			})
		);

		allocations.setData({
			allocations: []
		});

		const allocationIndex = allocations.vm.addAllocation({
			id: 999,
			slug: 'ABCDEF',
			fundId: 1,
			entryId: 4033,
			currency: 'EUR',
			name: 'Fund 2023',
			amount: '€ 987.00',
			rawAmount: 987,
			tags: [],
			paid: '€ 0.00',
			created_at: '2023-04-03',
			grantEndDate: null
		});

		allocations.setMethods({
			cancelAllocation: cancelAllocationSpy
		});

		allocations.vm.displayDeleteModalPrompt(allocationIndex);

		setTimeout(() => {
			expect(cancelAllocationSpy.calledOnce).to.be.true;
			expect(allocations.vm.displayDeleteModal).to.be.true;
			expect(allocations.vm.allocationToBeDeletedIndex).to.equal(allocationIndex);
			done();
		}, 200);
	});
});
