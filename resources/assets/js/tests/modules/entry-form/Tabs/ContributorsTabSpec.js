import apiMutations from '@/lib/store/modules/entry-form-api/mutations.js';
import ContributorsTab from '@/modules/entry-form/Tabs/ContributorsTab.vue';
import { expect } from 'chai';
import mutations from '@/lib/store/modules/entry-form/mutations.js';
import Vuex from 'vuex';
import { createLocalVue, shallowMount } from '@vue/test-utils';

const localVue = createLocalVue();
localVue.use(Vuex);

const lang = { get: () => '' };

const defaultStore = () => ({
	modules: {
		entryForm: {
			namespaced: true,
			state: {
				locks: {
					readOnly: false,
				},
			},
			getters: {
				visibleContributorFields: () => () => [],
				isLoaded: () => true,
				contributors: () => [],
				contributorFields: () => [],
			},
			mutations,
			actions: {
				addContributor: () => {},
				loadContributors: () => {},
			},
		},
		entryFormConfiguration: {
			namespaced: true,
			getters: {
				configurationMode: () => false,
			},
			state: {
				configurationMode: false,
			},
		},
		entryFormApi: {
			namespaced: true,
			state: {
				errorBag: [],
			},
			mutations: apiMutations,
		},
	},
});

const tab = {
	id: 1,
	resource: 'Entries',
	type: 'Contributors',
	maxContributors: 10,
};

describe('ContributorsTab', () => {
	it('disables button when the max limit is reached', async () => {
		const contributorsTab = shallowMount(ContributorsTab, {
			provide: { lang },
			propsData: {
				tab: tab,
				configure: false,
				access: { canEdit: true, canSave: true, canSubmit: true },
			},
			computed: {
				contributors() {
					return [{ id: 10 }, { id: 11 }];
				},
			},
			store: new Vuex.Store(defaultStore()),
			localVue,
		});

		const button = contributorsTab.find('button');

		expect(button.exists()).to.be.true;
		expect(button.attributes().disabled).to.be.undefined;

		await contributorsTab.setProps({ tab: { ...tab, maxContributors: 2 } });
		expect(button.attributes().disabled).to.equal('disabled');
	});

	it('removes tab errors when a contributor is added', () => {
		const storeObject = defaultStore();
		storeObject.modules.entryFormApi.state.errorBag = [
			{
				type: 'Tab',
				tabId: tab.id,
			},
		];
		storeObject.modules.entryForm.state.tabFieldsErrors = {};
		storeObject.modules.entryForm.state.tabApiErrors = [tab.id];

		const contributorsTab = shallowMount(ContributorsTab, {
			provide: { lang },
			propsData: {
				tab: tab,
				configure: false,
			},
			computed: {
				contributors() {
					return [];
				},
			},
			store: new Vuex.Store(storeObject),
			localVue,
		});

		expect(storeObject.modules.entryFormApi.state.errorBag.length).to.equal(1);

		contributorsTab.vm.addContributor(tab.id);

		expect(storeObject.modules.entryForm.state.tabApiErrors).to.deep.equal([]);
		expect(storeObject.modules.entryFormApi.state.errorBag).to.deep.equal([]);
	});
});
