import ConfigurationModeBreadcrumbs from '@/modules/entry-form/Configuration/ConfigurationModeBreadcrumbs.vue';
import Vuex from 'vuex';
import { createLocalVue, shallowMount } from '@vue/test-utils';
import { expect } from 'chai';

const localVue = createLocalVue();
localVue.use(Vuex);

const form = { name: 'Fake Name' };

const store = new Vuex.Store({
	modules: {
		entryForm: {
			namespaced: true,
			state: {
				form: form,
			},
		},
	},
});

const entryFormBreadcrumbs = [
	{ text: 'Manage entries', link: 'https://notaplicable/entry/manager', icon: null, name: 'index' },
	{ text: 'Entry Form', link: null, icon: null, name: 'form' },
	{ text: 'Start entry', link: null, icon: null, name: 'entry' },
];

const getWrapper = (canEditForms) =>
	shallowMount(ConfigurationModeBreadcrumbs, {
		propsData: {
			breadcrumbs: entryFormBreadcrumbs,
			canEditForms: canEditForms,
		},
		store,
		localVue,
	});

describe('ConfigurationModeBreadcrumbs', () => {
	it('should add settings icon', () => {
		const wrapper = getWrapper(true);

		const breadcrumb = wrapper.vm.formBreadcrumb;

		expect(breadcrumb.icon).to.equal('settings');
		expect(breadcrumb.text).to.equal(form.name);
	});

	it('should not add settings icon', () => {
		const wrapper = getWrapper(false);

		const breadcrumb = wrapper.vm.formBreadcrumb;

		expect(breadcrumb.icon).to.be.empty;
		expect(breadcrumb.text).to.equal(form.name);
	});
});
