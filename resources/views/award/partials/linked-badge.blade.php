<a href="{{ request()->fullUrlWithQuery(['badge' => (string) $badge->slug]) }}" class="award-badge">
    @if ($badge->isTextBadge())
        <div class="text" style="background-color: #{{ $badge->badgeColour }};">
            {{ lang($badge, 'badgeText') }}
        </div>
    @endif
    @if ($badge->isImageBadge())
        <img src="{{ imgix($badge->badgeImage->file, params: config('ui.images.awards.badge')) }}" alt="{{ lang($badge, 'badgeText') }}">
    @endif
</a>

