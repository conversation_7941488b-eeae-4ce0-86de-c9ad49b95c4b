<hr>
<div class="row">
    <div class="col-xs-12 col-md-6">
        <h2>{{ trans('integrations.form.crm.entry.heading') }}</h2>

        <h3>@lang('integrations.form.crm.entry.events.entry-submission')</h3>
        <div class="form-group">
            <div class="checkbox">
                <label for="entry-submission-create-record-entry">
                    {!! html()->checkbox('actions[entry-submission][]', $integration->actionEnabled('entry-submission', 'create-record-entry'), 'create-record-entry')->id('entry-submission-create-record-entry') !!}
                    @lang('integrations.form.crm.entry.actions.create-record-entry')
                </label>
            </div>

            <div class="checkbox">
                <label for="entry-submission-update-record-entry">
                    {!! html()->checkbox('actions[entry-submission][]', $integration->actionEnabled('entry-submission', 'update-record-entry'), 'update-record-entry')->id('entry-submission-update-record-entry') !!}
                    @lang('integrations.form.crm.entry.actions.update-record-entry')
                </label>
            </div>
        </div>
    </div>
    <div class="col-xs-12 col-md-6">
        @include('integration.crm.object', ['resource' => 'entry'])

        @include('integration.crm.partials.unique-field', ['resource' => 'entry', 'options' => $entryUniqueIdentifier])

        @include('integration.crm.partials.standard-entry-fields', ['resource' => 'entry'])

        @include('integration.crm.partials.custom-fields', ['resource' => 'entry', 'fields' => $entryFields])

        @include('integration.crm.partials.standard-user-fields', ['resource' => 'entry'])

        @include('integration.crm.partials.custom-fields', ['resource' => 'entry', 'fields' => $userFields])

        @include('integration.crm.partials.fixed-values', ['resource' => 'entry'])
    </div>
</div>
