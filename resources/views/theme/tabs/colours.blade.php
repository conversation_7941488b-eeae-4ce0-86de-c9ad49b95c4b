<tab-content>
    <panel frameless sticky-selector=".sticky-colors" :sticky-offset="60">
        <column :width="4">
            <div class="multiselect">
                <div class="multiselect-filter">
                    <input type="text" placeholder="@lang('theme.colours.placeholder')" class="form-control">
                </div>

                <div class="multiselect-options" style="height:auto; overflow:hidden;">
                    @foreach ($colors as $group => $colors)
                        <div class="multiselect-option header" style="padding-left: 50px; height: 45px; line-height: 30px;">@lang("theme.colours.headers.$group")</div>
                        @foreach ($colors as $key => $color)
                            <wired-color-list-item setting="{{$key}}">
                                @lang("theme.colours.keys.$key")
                            </wired-color-list-item>
                        @endforeach
                    @endforeach
                </div>
            </div>

            <wired-reset>@lang('theme.colours.reset')</wired-reset>
        </column>

        <column :width="4" class="sticky-colors">
            <wired-custom-col-selector :key="currentColorId" :setting="currentColorId"></wired-custom-col-selector>
            <p>{!! trans('theme.info.wcag.message') !!}</p>
            <wired-submit></wired-submit>
        </column>
    </panel>
</tab-content>
