@extends('layouts.pdf')

@section('content')
    <div class="row">
        <div class="col-xs-9">
            <p class="entry-category">
                <b>{{ lang($account, 'name') }} ({{ lang($season, 'name') }})</b><br>
                @if ($entry->displayId())
                    {{ local_id($entry).' '.lang($entry->category, 'name') }}
                @else
                    {{ lang($entry->category, 'name') }}
                @endif
                @if ($chapter)
                    (@lang('pdf.labels.chapter'): {{ lang($chapter, 'name') }})
                @endif
            </p>
            <h1 class="entry-title">{{ $entry->title }}</h1>
        </div>
        <div class="col-xs-3">
            @include('entry.pdf.qr-code', ['entry' => $entry])
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <hr class="hr"/>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <?php $showName = in_array('vip_judging', setting_explode('judging-display-entrant-name')); ?>
            @if ($showName || count($userFields))
                <h3>@lang('judging.view.entrant.details')</h3>
                @if ($showName)
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="field-title">@lang('judging.view.entrant.name')</div>
                            <div class="field-value">{{ $entry->user->name }}</div>
                        </div>
                    </div>
                @endif

                @foreach ($userFields as $field)
                    @include('entry.pdf.field', ['field' => $field])
                @endforeach

                <hr class="hr"/>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <h3>@lang('judging.view.entry.details')</h3>

            @foreach ($tabbedFields as $fields)
                @if ($tab = $fields->get('tab'))
                    @if ($tab->tabDividerOnPdfs)
                        <hr class="hr"/>
                        <h3>{{ lang($tab, 'name') }}</h3>
                    @endif
                    @foreach($fields->get('fields') as $field)
                        @include('entry.pdf.field', ['field' => $field])
                    @endforeach
                @endif
            @endforeach
        </div>
    </div>

    @if (!$contributors->isEmpty())
        <div class="row">
            <div class="col-xs-12">
                @include('entry.pdf.contributors', ['contributors' => $contributors])
            </div>
        </div>
    @endif

    @if (!$referees->isEmpty())
        <div class="row">
            <div class="col-xs-12">
                @include('entry.pdf.referees', ['referees' => $referees])
            </div>
        </div>
    @endif

    <div class="row">
        <div class="col-xs-12">
            <hr class="hr"/>
            <p>{!! trans('pdf.text.complete_attachments', ['link' => link_to(route('entry.entrant.edit', [$entry->slug]), $domain->domain)]) !!}</p>
        </div>
    </div>

    @include('entry.pdf.attachments', ['attachments' => $attachments, 'links' => $links])

@stop
