@if ($categoryCount <= 50)
    {!! html()->select('category', for_category_select($overrideCategories ?? $categories, $divisions ?? false, true), Request::get('category'))->class('form-control') !!}
@else
    <search-field
        id="category-selector"
        name="category-selector"
        hidden-input-name="category"
        src="{{ route('category.autocomplete', ['parents' => 'true', 'season' => !empty($categoriesSeason) ? $categoriesSeason : null, 'name' => '']) }}"
        :initial-id="@js($selectedSlug)"
        :initial-value="@js($selectedText)"
        value-property="title"
        searching-label="{{ trans('miscellaneous.searching') }}"
    ></search-field>
@endif
