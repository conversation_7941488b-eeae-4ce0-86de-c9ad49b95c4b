<div>
    @if(count($assignments))
        <table class="table markers-table">
            <thead>
            <tr>
                <th>{!! HTML::markerToggle() !!}</th>
                @include('field.columnator.button', ['type' => $columnatorType])
                <th class="text-nowrap">{!! HTML::sorter('id', trans('qualifying.table.columns.local_id')) !!}</th>
                <th>{!! HTML::sorter('title', trans('qualifying.table.columns.title')) !!}</th>
                <th>{!! HTML::sorter('consensus', trans('qualifying.table.columns.consensus')) !!}</th>
                @if ($showChapterColumn)
                    <th>@lang('qualifying.table.columns.chapter')</th>
                @endif
                <th>@lang('qualifying.table.columns.category')</th>
                <th>{!! HTML::sorter('pass', trans('qualifying.table.columns.pass')) !!}</th>
                <th>{!! HTML::sorter('fail', trans('qualifying.table.columns.fail')) !!}</th>
                @if ($scoreSet->allowUnsureDecisions)
                    <th>{!! HTML::sorter('unsure', trans('qualifying.table.columns.unsure')) !!}</th>
                @endif
                <th>{!! HTML::sorter('responses', trans('qualifying.table.columns.responses')) !!}</th>
                @include('field.columnator.header', ['columns' => $extraColumns])
            </tr>
            </thead>
            <tbody>
            @foreach ($assignments->items() as $i => $assignment)
                <tr>
                    <td>{!! HTML::markerCheckbox($assignment->entryId) !!}</td>
                    <td>{{-- Thumbnail column --}}</td>
                    <td class="text-nowrap"><a href="{{ $url = route('entry.manager.view', ['entry' => $assignment->entrySlug]) }}">{{ local_id($assignment->entry) }}</a></td>
                    <td>
                        <a href="{{ $url }}">{{ $assignment->entry->title }}</a>
                        @if (Consumer::can('view', 'Tags'))
                            <div class="labels">
                                {!! HTML::tagLabels($assignment->entry->tags) !!}
                            </div>
                        @endif
                    </td>
                    <td>{!! HTML::consensus($assignment->consensus) !!}</td>
                    @if ($showChapterColumn)
                        <td>{{ lang($assignment->entry->chapter, 'name') }}</td>
                    @endif
                    <td>{{ lang($assignment->entry->category, 'name') }}</td>
                    <td>{{ !empty($assignment->countPassed) ? $assignment->countPassed : '-' }}</td>
                    <td>{{ !empty($assignment->countFailed) ? $assignment->countFailed : '-' }}</td>
                    @if ($scoreSet->allowUnsureDecisions)
                        <td>{{ !empty($assignment->countUnsure) ? $assignment->countUnsure : '-' }}</td>
                    @endif
                    <td>{{ !empty($assignment->countResponses) ? $assignment->countResponses : '-' }}</td>
                    @include('field.columnator.row', ['columns' => $extraColumns, 'record' => $assignment])
                </tr>
            @endforeach
            </tbody>
        </table>

        <div class="row">
            <div class="col-xs-12">
                @include('partials.page.pagination', ['paginator' => $assignments])
            </div>
        </div>
    @else
        <div>
            <p>@lang('qualifying.table.empty')</p>
        </div>
    @endif
</div>
