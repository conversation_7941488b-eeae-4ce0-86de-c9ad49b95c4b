@section('title')
    {!! HTML::pageTitle([trans('users.titles.main'), trans('users.titles.new')]) !!}
@stop

@section('main')
    <div class="row island">
        <div class="col-xs-12">
            <div class="title">
                @include('partials.header.breadcrumbs', ['crumbs' => [
                    [trans('users.titles.main'), route('users.index')],
                    [trans('users.buttons.bulk_invite')],
                ]])
            </div>
        </div>
    </div>

    @include('partials.errors.message')
    @include('partials.errors.display', ['html' => true])


    <users-invite-form
            id="users-invite-form"
            :users-limit="@js($usersLimit)"
            :users-total="@js($usersTotal)"
            :roles="@js($roles)"
            :translations="@js($translations)"
            :errors="@jsObject($errors->getBag('default')->getMessages())"
            :values="@jsObject(old())"
    ></users-invite-form>
@stop
