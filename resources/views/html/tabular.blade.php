<div class="row">
    <div class="tabular-system" data-style="{{ $tabular->style() }}">
        @if (count($tabular->tabs()) > 1)
            <div class="tabs-legacy-wrapper island">
                    <div class="tabs tabs--legacy" id="tabsLegacy" role="tablist">
                        @foreach ($tabular->tabs() as $tab)
                            <button  aria-controls="{{ $tab->id() }}TabContent" role="tab" type="button" id="{{ $tab->id() }}Tab" class="{{ implode(' ', $tab->classes()) }}" data-tab="{{ $tab->view() }}">
                                <span>{{ $tab->name() }}</span>
                            </button>
                        @endforeach
                    </div>
            </div>
        @endif
        <div class="tabs-panels">
            @foreach ($tabular->tabs() as $tab)
                <div aria-labelledby="{{ $tab->id() }}Tab" role="tabpanel" class="tab-content {{ implode(' ', $tab->classes()) }}" id="{{ $tab->id() }}TabContent">
                    @include($tab->view())
                </div>
            @endforeach
        </div>
    </div>
</div>
