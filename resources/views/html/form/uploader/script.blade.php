<div id="uploader">

    <div class="sort-order hidden">999</div>

    @if ($uploader->isMultiSelect() && !$readOnly)
        <div>
            <div>
                <button type="button" class="upload-multi-button btn btn-secondary" data-uploader="{{ $uploaderId }}">
                    @lang('files.buttons.'.$resource)
                </button>
                @if ($extensions = $uploader->humanReadableExtensions())
                    <help-icon content="{{ trans('files.buttons.helptext', ['types' => $extensions]) }}"></help-icon>
                @endif
                <p class="hidden upload-file-limit">@lang('files.messages.file_limit')</p>
            </div>
        </div>
    @endif

    <script type="text/javascript">
        var uploaderOptions = '{!! obfuscate($uploader->options()) !!}';
    </script>

    <div id="upload-errors"></div>

    @if ($uploader->isMultiSelect() && !$readOnly)
        <div id="upload-templates" class="hidden">
            @include('html.form.uploader.file', ['id' => 'upload-template-replace', 'label' => 'files.labels.'.$resource, 'fields' => $fields, 'canDelete' => $canDelete])
        </div>
    @endif
</div>
