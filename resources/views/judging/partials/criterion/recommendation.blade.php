<div class="form-group">
    <fieldset>
        <legend class="sr-only">{!! field_label($criterion) !!}</legend>
        @foreach($criterion->recommendationsWithTranslations as $recommendation)
            <div class="radio styled">
                {!! html()->radio("scores[$criterion->id]", !is_null($score) && $recommendation->value === (float) $score, $recommendation->value)->id("scores[$criterion->id][$recommendation->key]")->disabled($lockScores) !!}
                <label for="scores[{{ $criterion->id }}][{{ $recommendation->key }}]">
                    {{ $recommendation->label }}
                </label>
            </div>
        @endforeach
    </fieldset>
</div>
