@section('title')
    @if ($entryName)
        {!! HTML::pageTitle([trans('judging.titles.score'), $model->title]) !!}
    @else
        {!! HTML::pageTitle([trans('judging.titles.score')]) !!}
    @endif
@stop

@section('main')
    <div class="row island">
        <div class="col-xs-12 col-md-6">
            <div class="title">
                @include('partials.header.breadcrumbs', ['crumbs' => [
                    [trans('judging.titles.main'), route('judging.index')],
                    [trans('judging.titles.score')],
                ]])
            </div>
        </div>
    </div>

    @include('entry.common.preview', [
        'entry' => $model,
        'fields' => $fields,
        'contributors' => $contributors,
        'attachments' => $attachments,
        'links' => $links,
        'backButtonUrl' => null,
        'plagiarismScans' => null,
    ])
@stop
