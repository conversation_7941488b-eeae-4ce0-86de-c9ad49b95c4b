<?php
$classes = [];

if ($item->isParent()) {
    $classes[] = 'parent';
}
if ($item->isActive()) {
    $classes[] = 'active';
}
?>
<li @if ($classes) class="{{ implode(' ', $classes) }}"@endif>
    @if ($item->isRenderable())
        @if ($item->isParent())
            <a class="submenu-title" role="button">
                @if ($item->icon())
                    <span class="submenu-icon af-icons af-icons-{{ $item->icon() }}"></span>
                @endif
                <span class="text">{{ $item->text }}</span>
                <span class="af-icons af-icons-plus"></span>
            </a>
            {!! HTML::menu($item->name, 'partials.menu.menu') !!}
        @else
            <a href="{{ $item->link }}"@if ($item->isActive()) class="active"@endif>
                @if ($item->icon())
                    <span class="af-icons af-icons-{{ $item->icon() }}"></span>
                @endif
                <span class="text">{{ $item->text }}</span>
            </a>
        @endif
    @endif
</li>
