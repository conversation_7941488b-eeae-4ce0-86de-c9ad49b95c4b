<div class="row">
    <div class="col-xs-12 col-md-6">
        <div class="panel panel-default">
            <div class="panel-body">
                <div class="form-group {{ FormError::classIfErrorTranslated('name', Consumer::languageCode()) }}">
                    {!! html()->label(trans('document-templates.form.name.label'), 'name') !!}
                    {!! Multilingual::text('name', $documentTemplate, ['class' => 'form-control']) !!}
                </div>

                <div class="form-group">
                    {!! html()->label(trans('document-templates.form.description.label'), 'description') !!}
                    @include('html.multilingual', [
                        'resource' => ['translated' => $documentTemplate->translated],
                        'field' => 'description',
                        'class' => 'form-control',
                        'disabled' => !empty($readOnly)
                    ])

                </div>

                <div class="{{ FormError::classIfError('fileToken') }}">
                    <file-field
                        title="{{ trans('document-templates.form.file.label') }}"
                        name="file"
                        :field="@jsObject($files)"
                        resource-slug="Document Templates"
                        :uploader-options="@js(obfuscate($uploaderOptions)->toHtml())"
                        :document-template="@jsObject($documentTemplate)"
                        help-text="{{ trans('document-templates.form.file.help') }}"
                    ></file-field>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xs-12 col-md-6">
        <div class="panel panel-default">
            <div class="panel-body">
                <h4 style="margin-top: 0;">@lang('document-templates.form.available-merge-fields.label')</h4>
                @foreach($mergeFields as $title => $fields)
                    <h5>@lang("documents.types.{$title}.label")</h5>
                    <ul class="list-unstyled">
                        @foreach($fields as $field)
                            <li>
                                <popover content="@lang('shared.copied_to_clipboard')" wrapper-tag="span" placement="right" :auto-hide="true">
                                    <button type="button" class="btn-link" @click.stop="copyToClipboard('{{ '{'.$field.'}' }}')">{{ '{'.$field.'}' }}</button>
                                </popover>
                            </li>
                        @endforeach
                    </ul>
                @endforeach
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xs-12">
        <div class="form-actions">
            @include('html.buttons.save', ['buttonDisabled' => !$canSave])
            @include('html.buttons.cancel', ['route' => 'document-template.index'])
        </div>
    </div>
</div>
