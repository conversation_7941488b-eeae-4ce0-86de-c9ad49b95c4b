<?php

namespace Tests\Modules\AllocationPayments\Listeners;

use AwardForce\Modules\AllocationPayments\Events\AllocationPaymentStatusWasChanged;
use AwardForce\Modules\AllocationPayments\Listeners\AllocationPaymentsStatusListener;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Notifications\Commands\SendSpecificNotificationCommand;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\PaymentMethods\Models\PaymentMethod;
use Illuminate\Support\Facades\Bus;
use Platform\Events\EventDispatcher;
use Tests\IntegratedTestCase;

final class AllocationPaymentStatusListenerTest extends IntegratedTestCase
{
    use EventDispatcher;

    public function testSendNotificationScheduledWhenAllocationPaymentStatusIsChangedToTheOneSetInNotification(): void
    {
        $this->muffin(Notification::class, [
            'trigger' => 'payment.status.changed',
            'payment_status_option' => 'failed',
        ]);

        $allocationPayment = $this->getAllocationPayment();
        $allocationPayment->status = 'failed';
        $allocationPayment->save();

        $listener = app(AllocationPaymentsStatusListener::class);

        Bus::fake();

        $listener->whenAllocationPaymentStatusWasChanged(
            new AllocationPaymentStatusWasChanged($allocationPayment, $allocationPayment->status)
        );

        Bus::assertDispatchedTimes(SendSpecificNotificationCommand::class, 1);
    }

    private function getAllocationPayment(?Form $form = null): AllocationPayment
    {
        $paymentMethod = $this->muffin(PaymentMethod::class);
        $allocation = $this->muffin(Allocation::class, [
            'entry_id' => $this->muffin(Entry::class, [
                'form_id' => $form?->id ?? FormSelector::getId(),
            ])->id,
        ]);

        return AllocationPayment::add(
            $this->season->id,
            $paymentMethod->id,
            'scheduled',
            '#123456',
            100,
            'EUR',
            $allocation->id,
            $allocation->entryId,
            $allocation->fundId,
            now()->addMinutes(10)->format('Y-m-d'),
            null
        );
    }

    public function testItDoesNotSendNotificationScheduledWhenAllocationPaymentStatusIsChangedToAnotherSetInNotification(): void
    {
        $this->muffin(Notification::class, [
            'trigger' => 'payment.status.changed',
            'payment_status_option' => 'failed',
        ]);

        $allocationPayment = $this->getAllocationPayment();
        $allocationPayment->status = 'scheduled';
        $allocationPayment->save();

        $listener = app(AllocationPaymentsStatusListener::class);

        Bus::fake();

        $listener->whenAllocationPaymentStatusWasChanged(
            new AllocationPaymentStatusWasChanged($allocationPayment, $allocationPayment->status)
        );

        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);
    }

    public function testItSendsNotificationOnlyOnSelectedForms(): void
    {
        $notification = $this->muffin(Notification::class, [
            'trigger' => 'payment.status.changed',
            'payment_status_option' => 'paid',
        ]);
        $selected = $this->muffin(Notification::class, [
            'trigger' => 'payment.status.changed',
            'payment_status_option' => 'paid',
            'form_option' => 'select',
        ]);

        $selectedForm = $this->muffin(Form::class);
        $selected->forms()->sync($selectedForm->id);

        $otherNotification = $this->muffin(Notification::class, [
            'trigger' => 'payment.status.changed',
            'payment_status_option' => 'paid',
            'form_option' => 'select',
        ]);

        $otherNotification->forms()->sync($this->muffin(Form::class)->id);

        $allocationPayment = $this->getAllocationPayment($selectedForm);
        $allocationPayment->status = 'paid';
        $allocationPayment->save();

        $listener = app(AllocationPaymentsStatusListener::class);

        Bus::fake();

        $listener->whenAllocationPaymentStatusWasChanged(
            new AllocationPaymentStatusWasChanged($allocationPayment, $allocationPayment->status)
        );

        Bus::assertDispatched(
            SendSpecificNotificationCommand::class,
            fn(SendSpecificNotificationCommand $command) => in_array($command->notification->id, [$notification->id, $selected->id])
        );

        Bus::assertNotDispatched(
            SendSpecificNotificationCommand::class,
            fn(SendSpecificNotificationCommand $command) => $command->notification->id === $otherNotification->id
        );
    }
}
