<?php

namespace Tests\Modules\Clear\Views;

use AwardForce\Modules\Clear\Views\SocialLoginProviders;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Tests\IntegratedTestCase;

final class SocialLoginProvidersTest extends IntegratedTestCase
{
    public function testSocialProviders(): void
    {
        app(SettingRepository::class)->saveSetting('social-authentication', 'twitter,google,linkedin');
        $this->assertCount(3, $this->viewStub()->socialProviders());

        $twitter = $this->viewStub()->socialProviders()[0];
        $google = $this->viewStub()->socialProviders()[1];

        $this->assertEquals('twitter', $twitter['name']);
        $this->assertNotNull($twitter['url']);
        $this->assertNull($twitter['logoUrl']);
        $this->assertNull($twitter['logoAlt']);

        $this->assertEquals('google', $google['name']);
        $this->assertNotNull($twitter['url']);
        $this->assertNotNull($google['logoUrl']);
        $this->assertNotNull($google['logoAlt']);
    }

    private function viewStub()
    {
        return new class
        {
            use SocialLoginProviders;
        };
    }
}
