<?php

namespace Tests\Modules\Clear\Views;

use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Clear\Views\LoginView;
use Illuminate\Http\Request;
use Mockery as m;
use Tests\IntegratedTestCase;

final class LoginViewTest extends IntegratedTestCase
{
    private LoginView $view;

    public function init()
    {
        $this->view = app(LoginView::class);
    }

    public function testRegistersTranslations(): void
    {
        $this->assertArrayHasKey('en_GB.home', VueData::getTranslations());
    }

    public function testProvidesCountriesWithLeadingZeros(): void
    {
        $this->assertIsArray($this->view->countriesWithLeadingZeros);
        $this->assertNotEmpty($this->view->countriesWithLeadingZeros);
    }

    public function testProvidesRequestedRole(): void
    {
        $request = m::spy(Request::class);
        app()->instance('request', $request);
        $request->shouldReceive('route')->with('requestedRole')->andReturn('roleSlug');

        $this->assertEquals('roleSlug', $this->view->requestedRole);
    }
}
