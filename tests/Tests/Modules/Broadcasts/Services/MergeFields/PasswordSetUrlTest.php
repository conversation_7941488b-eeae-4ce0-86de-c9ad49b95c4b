<?php

namespace Tests\Modules\Broadcasts\Services\MergeFields;

use AwardForce\Library\Mail\Values\Recipient;
use AwardForce\Modules\Broadcasts\Services\MergeFields\PasswordSetUrl;
use Mockery as m;
use Platform\Language\Language;
use Platform\Support\Values\Email;
use Platform\Tokens\TokenManager;
use Tests\UnitTestCase;

final class PasswordSetUrlTest extends UnitTestCase
{
    /** @var Recipient */
    protected $recipient;

    /** @var TokenManager */
    protected $tokens;

    public function init()
    {
        $this->tokens = m::mock(TokenManager::class);

        $this->recipient = new Recipient(
            new Email('test@recipient.af4'),
            'Test',
            'Recipient',
            new Language('en_GB'),
            ['user_id' => 1]
        );
    }

    public function testFill(): void
    {
        $content = 'Password set URL - {password_set_url}';
        $token = 'LJaNRHj9EZecCBC0t7185DqRZKDv6pox';

        $this->tokens->shouldReceive('create')->andReturn($token);

        $passwordSetUrl = new PasswordSetUrl($this->tokens);

        $content = $passwordSetUrl->fill($content, $this->recipient);

        $this->assertStringContainsString($token, $content);
    }
}
