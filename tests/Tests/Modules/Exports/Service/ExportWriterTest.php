<?php

namespace Tests\Modules\Exports\Service;

use AwardForce\Modules\Exports\Services\ExportWriter;
use AwardForce\Modules\Exports\Services\Sheets\Sheet;
use Mockery as m;
use Spatie\SimpleExcel\SimpleExcelReader;
use Spatie\SimpleExcel\SimpleExcelWriter;
use Tests\UnitTestCase;

class ExportWriterTest extends UnitTestCase
{
    public function testDuplicateSheetWithSameEndingingTitleAsSheetIndex()
    {
        $randomTitle = '6IkD6cY6cTNI1pvc4s0NTVyj6Yx8Na222'; //ending in 2 which is the index of duplicated sheet
        $fileName = 'test.xlsx';

        $sheet1 = m::mock(Sheet::class);
        $sheet2 = m::mock(Sheet::class);

        $sheet1->shouldReceive('title')->once()->andReturn($randomTitle);
        $sheet2->shouldReceive('title')->once()->andReturn($randomTitle);
        $sheet1->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));
        $sheet2->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));

        $exportWriter = new ExportWriter($fileName);
        $exportWriter->sheet($sheet1);
        $exportWriter->sheet($sheet2);

        $exportWriter->write();
        $exportWriter->close();

        $this->assertFileExists($fileName);
    }

    public function testItWritesAllSheets(): void
    {
        $fileName = 'test.xlsx';
        $sheet1 = m::mock(Sheet::class);
        $sheet2 = m::mock(Sheet::class);
        $sheet3 = m::mock(Sheet::class);
        $sheet4 = m::mock(Sheet::class);
        $sheet5 = m::mock(Sheet::class);
        $sheet6 = m::mock(Sheet::class);
        $sheet7 = m::mock(Sheet::class);

        $sheet1->shouldReceive('title')->once()->andReturn('Sheet');
        $sheet2->shouldReceive('title')->once()->andReturn('Sheet 2');
        // Added utf8 check
        $sheet3->shouldReceive('title')->once()->andReturn('" Test *πάνω [clean:Title] method and more text that will be truncated"');
        // Test duplicate sheet title
        $sheet4->shouldReceive('title')->once()->andReturn('Sheet');
        $sheet5->shouldReceive('title')->once()->andReturn('Sheet');

        //Test duplicate increment on max length
        //Added trailing F to make sure the last character is not the same number of the sheet to avoid duplicate error
        $randomTitle = str_random(30).'F';
        $sheet6->shouldReceive('title')->once()->andReturn($randomTitle);
        $sheet7->shouldReceive('title')->once()->andReturn($randomTitle);

        $sheet1->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));
        $sheet2->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));
        $sheet3->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));
        $sheet4->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));
        $sheet5->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));
        $sheet6->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));
        $sheet7->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));

        $exportWriter = new ExportWriter($fileName);
        $exportWriter->sheet($sheet1);
        $exportWriter->sheet($sheet2);
        $exportWriter->sheet($sheet3);
        $exportWriter->sheet($sheet4);
        $exportWriter->sheet($sheet5);
        $exportWriter->sheet($sheet6);
        $exportWriter->sheet($sheet7);
        $exportWriter->write();
        $exportWriter->close();

        $this->assertFileExists($fileName);

        $reader = SimpleExcelReader::create($fileName);
        $reader->getReader()->open($fileName);

        $this->assertCount(7, $reader->getReader()->getSheetIterator());
        $this->assertTrue($reader->hasSheet('Sheet'));
        $this->assertTrue($reader->hasSheet('Sheet 2'));
        $this->assertTrue($reader->hasSheet('Test πάνω cleanTitle method and'));
        $this->assertTrue($reader->hasSheet('Sheet4'));
        $this->assertTrue($reader->hasSheet('Sheet5'));
        $this->assertTrue($reader->hasSheet($randomTitle));
        $this->assertTrue($reader->hasSheet(substr($randomTitle, 0, 30).'7'));

        unlink($fileName);
    }

    public function testCsvAsSingleSheet(): void
    {
        $fileName = 'test.csv';
        $sheet1 = m::mock(Sheet::class);
        $sheet2 = m::mock(Sheet::class);
        $sheet1->shouldNotReceive('title');
        $sheet2->shouldNotReceive('title');
        $sheet1->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));
        $sheet2->shouldReceive('write')->once()->with(m::type(SimpleExcelWriter::class));

        $exportWriter = new ExportWriter($fileName);
        $exportWriter->sheet($sheet1);
        $exportWriter->sheet($sheet2);
        $exportWriter->write();
        $exportWriter->close();

        $this->assertFileExists($fileName);

        $reader = SimpleExcelReader::create($fileName);
        $reader->getReader()->open($fileName);

        $this->assertCount(1, $reader->getReader()->getSheetIterator());

        unlink($fileName);
    }
}
