<?php

namespace Tests\Modules\ReviewFlow\Commands;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\ReviewFlow\Commands\ManageEntryReviewTaskStatus;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use Illuminate\Support\Facades\Bus;
use Platform\Bus\Commands\UndeleteModelsCommand;

final class UndeleteReviewStageCommandTest extends ReviewStageCommand
{
    public function testRaisesDeleteReviewTasks(): void
    {
        Bus::fake();
        $entry = $this->muffin(Entry::class);
        $reviewStage = $this->muffin(ReviewStage::class);
        $this->muffin(ReviewTask::class, [
            'review_stage_id' => $reviewStage->id,
            'entry_id' => $entry->id,
        ]);
        $reviewStage->delete();
        $this->undeleteModelsHandler->handle(new UndeleteModelsCommand([$reviewStage->id], $this->reviewStageRepo));
        Bus::assertDispatched(ManageEntryReviewTaskStatus::class);
    }
}
