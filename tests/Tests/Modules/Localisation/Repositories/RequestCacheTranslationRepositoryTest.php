<?php

namespace Tests\Modules\Localisation\Repositories;

use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Localisation\Repositories\EloquentTranslationRepository;
use AwardForce\Modules\Localisation\Repositories\RequestCacheTranslationRepository;
use Illuminate\Support\Collection;
use Tectonic\Localisation\Translator\ResourceCriteria;
use Tests\IntegratedTestCase;

final class RequestCacheTranslationRepositoryTest extends IntegratedTestCase
{
    private ResourceCriteria $criteria;

    public function init()
    {
        $this->criteria = new ResourceCriteria();

        $this->fields = $this->muffins(3, Field::class);
        $this->fields[0]->saveTranslation('el_GR', 'title', 'Greek Field 1', current_account_id());
        $this->categories = $this->muffins(2, Category::class);

        foreach ([...$this->fields, ...$this->categories] as $resource) {
            $this->criteria->addResource(class_basename($resource));
            $this->criteria->addId(class_basename($resource), $resource->id);
        }
    }

    public function testCachesTranslationByResult(): void
    {
        $repository = app(EloquentTranslationRepository::class);
        $cacheRepository = app(RequestCacheTranslationRepository::class);

        $results = $this->modifyResults($repository->getByResourceCriteria($this->criteria));

        $cacheResults = $this->modifyResults($cacheRepository->getByResourceCriteria($this->criteria));

        $this->assertCount(2, $results);
        $this->assertCount(3, $results['Field']);
        $this->assertCount(2, $results['Category']);
        $this->assertEquals($results, $cacheResults);

        \DB::table('translations')
            ->where('resource', 'Field')
            ->where('foreign_id', $this->fields[0]->id)
            ->where('language', 'el_GR')
            ->where('field', 'title')
            ->update(['value' => 'Greek Field 1 but different']);

        $newCategory = $this->muffin(Category::class);
        $this->criteria->addId('Category', $newCategory->id);

        $results = $this->modifyResults($repository->getByResourceCriteria($this->criteria));
        $cacheResults = $this->modifyResults($cacheRepository->getByResourceCriteria($this->criteria));

        $this->assertCount(2, $cacheResults);
        $this->assertCount(3, $cacheResults['Field']);
        $this->assertCount(3, $cacheResults['Category']);
        $this->assertEquals($results['Category'], $cacheResults['Category']);
        $this->assertNotEquals($results, $cacheResults);

        $greekTranslation = $results['Field'][$this->fields[0]->id]->where('language', 'el_GR')->first();
        $greekCachedTranslation = $cacheResults['Field'][$this->fields[0]->id]->where('language', 'el_GR')->first();

        $this->assertEquals('Greek Field 1 but different', $greekTranslation->value);
        $this->assertEquals('Greek Field 1', $greekCachedTranslation->value);
    }

    private function modifyResults(Collection $results): Collection
    {
        return $results
            ->sortBy('foreign_id')
            ->groupBy('resource')
            ->map(fn($group) => collect($group)->groupBy('foreign_id')->sortBy('field'));
    }
}
