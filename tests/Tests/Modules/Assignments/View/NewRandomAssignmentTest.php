<?php

namespace Tests\Modules\Assignments\View;

use AwardForce\Http\Requests\Assignment\AssignmentEntrySearchRequest;
use AwardForce\Modules\Assignments\View\NewRandomAssignment;
use AwardForce\Modules\Categories\Models\Category;
use Tests\IntegratedTestCase;

final class NewRandomAssignmentTest extends IntegratedTestCase
{
    public function testCategories(): void
    {
        collect($this->muffins(2, Category::class));

        $view = app(NewRandomAssignment::class, ['request' => new AssignmentEntrySearchRequest()]);

        $this->assertJson($view->categories());
        $this->assertStringNotContainsString('children', $view->categories());
        $this->assertStringContainsString('id', $view->categories());
        $this->assertStringContainsString('name', $view->categories());
    }
}
