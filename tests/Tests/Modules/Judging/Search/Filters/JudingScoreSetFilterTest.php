<?php

namespace Tests\Modules\Judging\Search\Filters;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\GuestConsumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Assignments\Models\AssignmentRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Judging\Search\Filters\JudgingScoreSetFilter;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\SearchFilterCollection;
use Platform\Search\SearchInterface;
use Tests\IntegratedTestCase;

final class JudingScoreSetFilterTest extends IntegratedTestCase
{
    public function testReturnsGivenInputScoreSetId(): void
    {
        $judge = $this->setupUserWithRole('Judge');
        Consumer::set(new UserConsumer($judge));

        $scoreSets = $this->muffins(3, ScoreSet::class, ['mode' => ScoreSet::MODE_VIP]);

        $entry1 = $this->muffin(Entry::class);
        $entry2 = $this->muffin(Entry::class);
        $entry3 = $this->muffin(Entry::class);

        $this->muffin(Assignment::class, ['entry_id' => $entry3->id, 'score_set_id' => $scoreSets[0]->id, 'judge_id' => $judge->id]);
        $this->muffin(Assignment::class, ['entry_id' => $entry2->id, 'score_set_id' => $scoreSets[0]->id, 'judge_id' => $judge->id]);
        $this->muffin(Assignment::class, ['entry_id' => $entry1->id, 'score_set_id' => $scoreSets[0]->id, 'judge_id' => $judge->id]);

        $this->muffin(Assignment::class, ['entry_id' => $entry3->id, 'score_set_id' => $scoreSets[1]->id, 'judge_id' => $judge->id]);
        $this->muffin(Assignment::class, ['entry_id' => $entry2->id, 'score_set_id' => $scoreSets[1]->id, 'judge_id' => $judge->id]);
        $this->muffin(Assignment::class, ['entry_id' => $entry1->id, 'score_set_id' => $scoreSets[1]->id, 'judge_id' => $judge->id]);

        $this->muffin(Assignment::class, ['entry_id' => $entry3->id, 'score_set_id' => $scoreSets[2]->id, 'judge_id' => $judge->id]);
        $this->muffin(Assignment::class, ['entry_id' => $entry2->id, 'score_set_id' => $scoreSets[2]->id, 'judge_id' => $judge->id]);
        $this->muffin(Assignment::class, ['entry_id' => $entry1->id, 'score_set_id' => $scoreSets[2]->id, 'judge_id' => $judge->id]);

        $result = (new JudgingSearch($scoreSets))->fromInput(['score-set' => $scoreSets[0]->id]);

        $this->assertCount(3, $result);
        $this->assertEquals($scoreSets[0]->id, $result[0]->scoreSet->id);
        $this->assertEquals($scoreSets[0]->id, $result[1]->scoreSet->id);
        $this->assertEquals($scoreSets[0]->id, $result[2]->scoreSet->id);
    }

    public function testReturnsGivenInputScoreSetIdWhenGuest(): void
    {
        Consumer::set(new GuestConsumer(null));
        $scoreSets = $this->muffins(3, ScoreSet::class, ['mode' => ScoreSet::MODE_VIP]);

        $entry1 = $this->muffin(Entry::class);
        $entry2 = $this->muffin(Entry::class);
        $entry3 = $this->muffin(Entry::class);

        $this->muffin(Assignment::class, ['entry_id' => $entry3->id, 'score_set_id' => $scoreSets[0]->id, 'judge_id' => null]);
        $this->muffin(Assignment::class, ['entry_id' => $entry2->id, 'score_set_id' => $scoreSets[0]->id, 'judge_id' => null]);
        $this->muffin(Assignment::class, ['entry_id' => $entry1->id, 'score_set_id' => $scoreSets[0]->id, 'judge_id' => null]);

        $this->muffin(Assignment::class, ['entry_id' => $entry3->id, 'score_set_id' => $scoreSets[1]->id, 'judge_id' => null]);
        $this->muffin(Assignment::class, ['entry_id' => $entry2->id, 'score_set_id' => $scoreSets[1]->id, 'judge_id' => null]);
        $this->muffin(Assignment::class, ['entry_id' => $entry1->id, 'score_set_id' => $scoreSets[1]->id, 'judge_id' => null]);

        $this->muffin(Assignment::class, ['entry_id' => $entry3->id, 'score_set_id' => $scoreSets[2]->id, 'judge_id' => null]);
        $this->muffin(Assignment::class, ['entry_id' => $entry2->id, 'score_set_id' => $scoreSets[2]->id, 'judge_id' => null]);
        $this->muffin(Assignment::class, ['entry_id' => $entry1->id, 'score_set_id' => $scoreSets[2]->id, 'judge_id' => null]);

        $result = (new JudgingSearch($scoreSets))->fromInput(['score-set' => $scoreSets[0]->id]);

        $this->assertCount(0, $result);
    }
}

class JudgingSearch implements SearchInterface
{
    /** @var ScoreSet */
    private $scoreSets;

    public function __construct($scoreSets)
    {
        $this->scoreSets = $scoreSets;
    }

    public function fromInput(array $input = [], $paginate = true)
    {
        $filters = (new SearchFilterCollection)->add(new JudgingScoreSetFilter($this->scoreSets, $input))
            ->add(new IncludeFilter('scoreSet'));

        return app(AssignmentRepository::class)->getByFilters($filters, false);
    }
}
