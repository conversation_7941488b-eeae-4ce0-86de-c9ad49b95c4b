<?php

namespace Tests\Modules\Files\Services\Thumbnails\Detectors;

use AwardForce\Modules\Files\Services\Thumbnails\Detectors\YoutubeDetector;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\UnitTestCase;

class YoutubeDetectorTest extends UnitTestCase
{
    protected function thumbnailUrl(): string
    {
        return 'https://img.youtube.com/vi/nCwRJUg3tcQ/0.jpg';
    }

    #[TestWith(['https://www.youtube.com/watch?v=', 'nCwRJUg3tcQ'])]
    #[TestWith(['https://youtube.com/v/', 'nCwRJUg3tcQ'])]
    #[TestWith(['https://youtube.com/vi/', 'nCwRJUg3tcQ'])]
    #[TestWith(['https://youtube.com/?v=', 'nCwRJUg3tcQ'])]
    #[TestWith(['https://youtube.com/?vi=', 'nCwRJUg3tcQ'])]
    #[TestWith(['https://youtube.com/watch?v=', 'nCwRJUg3tcQ'])]
    #[TestWith(['https://youtube.com/watch?vi=', 'nCwRJUg3tcQ'])]
    #[TestWith(['https://youtu.be/', 'nCwRJUg3tcQ'])]
    #[TestWith(['https://youtube.com/embed/', 'nCwRJUg3tcQ'])]
    #[TestWith(['https://www.youtube.com/v/', 'nCwRJUg3tcQ'])]
    public function testDetectsUrl(string $url, string $id)
    {
        $detector = (new YoutubeDetector)->with($url.$id);

        $this->assertTrue($detector->matches());
        $this->assertSame($this->thumbnailUrl(), $detector->thumbnail());
        $this->assertSame($id, $detector->id());
    }

    public function testReturnsFalseWhenUrlIsNotUrlType()
    {
        $url = 'https://example.com';
        $detector = new YoutubeDetector;

        $this->assertFalse($detector->with($url)->matches());
    }
}
