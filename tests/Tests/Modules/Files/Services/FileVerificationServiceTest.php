<?php

namespace Tests\Modules\Files\Services;

use AwardForce\Library\Filesystem\Storage;
use AwardForce\Modules\Files\Services\FileVerificationService;
use Illuminate\Contracts\Config\Repository as Config;
use Mockery as m;
use Tests\UnitTestCase;

final class FileVerificationServiceTest extends UnitTestCase
{
    const MISSINGFILE = 'thisfile/is.missing';

    const MISSINGFOLDER = 'thisfolderismissing/';

    const EXISTINGFILE = 'thisfile/does.exist';

    const EXISTINGFILE2 = 'thisfile/also.exists';

    const EXISTINGFILE3 = 'another/existing.file';

    /**
     * @var \AwardForce\Modules\Files\Services\FileVerificationService
     */
    protected $service;

    /**
     * @var m\MockInterface
     */
    protected $files;

    /**
     * @var m\MockInterface
     */
    protected $config;

    public function init()
    {
        $this->files = m::mock(Storage::class);
        $this->config = m::mock(Config::class);

        $this->service = new FileVerificationService($this->files, $this->config);
    }

    public function testVerifyMissingFile(): void
    {
        foreach (['', null, self::MISSINGFILE, self::MISSINGFOLDER] as $file) {
            $this->files->shouldReceive('exists')->with($file)->andReturn(false);
            $this->assertFalse($this->service->verify($file));
        }
    }

    public function testVerifyValidFile(): void
    {
        $this->files->shouldReceive('exists')->with(self::EXISTINGFILE)->once()->andReturn(true);

        $this->assertTrue($this->service->verify(self::EXISTINGFILE));
    }

    public function testVerifyValidFilesAllValid(): void
    {
        $files = [self::EXISTINGFILE, self::EXISTINGFILE2, self::EXISTINGFILE3];

        $this->files->shouldReceive('exists')->with(self::EXISTINGFILE)->once()->andReturn(true);
        $this->files->shouldReceive('exists')->with(self::EXISTINGFILE2)->once()->andReturn(true);
        $this->files->shouldReceive('exists')->with(self::EXISTINGFILE3)->once()->andReturn(true);

        $this->assertTrue($this->service->verifyFiles($files));
    }

    public function testVerifyValidFilesOneMissing(): void
    {
        $files = [self::EXISTINGFILE, self::MISSINGFILE, self::EXISTINGFILE3];

        $this->files->shouldReceive('exists')->with(self::EXISTINGFILE)->once()->andReturn(true);
        $this->files->shouldReceive('exists')->with(self::MISSINGFILE)->once()->andReturn(false);
        $this->files->shouldReceive('exists')->with(self::EXISTINGFILE3)->once()->andReturn(true);

        $this->assertFalse($this->service->verifyFiles($files));
    }

    public function testVerifyValidFilesMissingReturned(): void
    {
        $files = [self::EXISTINGFILE, self::MISSINGFILE, self::EXISTINGFILE3];

        $this->files->shouldReceive('exists')->with(self::EXISTINGFILE)->once()->andReturn(true);
        $this->files->shouldReceive('exists')->with(self::MISSINGFILE)->once()->andReturn(false);
        $this->files->shouldReceive('exists')->with(self::EXISTINGFILE3)->once()->andReturn(true);

        $this->assertFalse($this->service->verifyFiles($files, $missing));

        $this->assertEquals([self::MISSINGFILE], $missing);
    }

    public function testValidFile(): void
    {
        $this->config->shouldReceive('get')->with('filetypes.images')->andReturn([
            'gif' => 'image/gif',
            'jpg' => ['image/jpeg', 'image/jpg'],
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
        ]);
        $this->config->shouldReceive('get')->with('filetypes.videos')->andReturn(['mp4' => 'video/mp4']);

        $this->assertTrue($this->service->validate('file.jpg', 'image/jpeg', 'images.jpg'));
        $this->assertTrue($this->service->validate('file.jpg', 'image/jpeg', 'images'));
        $this->assertTrue($this->service->validate('file.jpg', 'image/jpeg', ['videos', 'images']));
        $this->assertTrue($this->service->validate('file.jpg', 'image/jpeg', ['images' => ['jpg', 'jpeg', 'gif']]));
    }

    public function testInvalidFile(): void
    {
        $this->config->shouldReceive('get')->with('filetypes.images')->andReturn([
            'gif' => 'image/gif',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
        ]);
        $this->config->shouldReceive('get')->with('filetypes.videos')->andReturn(['mp4' => 'video/mp4']);

        $this->assertFalse($this->service->validate('file.jpog', 'image/jpeg', 'images.jpg'));
        $this->assertFalse($this->service->validate('file.jpg', 'image/jpig', 'images.jpg'));
        $this->assertFalse($this->service->validate('file.pdf', 'image/jpeg', 'images'));
        $this->assertFalse($this->service->validate('file.jpg', 'video/jpeg', 'images'));
        $this->assertFalse($this->service->validate('file.doc', 'application/msword', ['videos', 'images']));
        $this->assertFalse($this->service->validate('file.jpg', 'image/jpeg', ['images' => ['jpeg', 'gif']]));
        $this->assertFalse($this->service->validate('file.jpeg', 'image/jpeg', ['images' => ['jpg', 'gif']]));
    }
}
