<?php

namespace Tests\Auth\Integrations;

use AwardForce\Auth\Exceptions\UnknownIntegrationProviderException;
use AwardForce\Auth\Integrations\ProviderManager;
use AwardForce\Auth\Integrations\Providers\Salesforce;
use Tests\BaseTestCase;

final class ProviderManagerTest extends BaseTestCase
{
    private ProviderManager $providerManager;

    public function init()
    {
        $this->providerManager = new ProviderManager;
    }

    public function testFromDriverWhenExists(): void
    {
        $this->assertInstanceOf(Salesforce::class, $this->providerManager->fromDriver('salesforce'));
    }

    public function testFromDriverWhenNotExists(): void
    {
        $this->expectException(UnknownIntegrationProviderException::class);
        $this->expectExceptionMessage('Unknown integration provider [some driver] requested.');
        $this->providerManager->fromDriver('some driver');
    }
}
