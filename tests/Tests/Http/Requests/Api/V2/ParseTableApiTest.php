<?php

namespace Tests\Http\Requests\Api\V2;

use AwardForce\Http\Requests\Api\V2\Entry\ParseTableFieldApi;
use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\Forms\Fields\Configurations\Table\Filter;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Http\Validation\TableFieldValidator;
use Eloquence\Behaviours\Slug;
use Illuminate\Validation\ValidationException;
use Tests\IntegratedTestCase;

final class ParseTableApiTest extends IntegratedTestCase
{
    private array $columnRules;
    private array $rowRules;
    private bool $dynamicRowsEnabled;
    private array $filters = [];
    private ParseTableFieldApi $parseTableFieldApi;
    private mixed $values;
    private string $fieldName;

    public function init(): void
    {
        $this->columnRules = ['column-aTlqZ', 'column-pWPaS', 'column-ZxYfv', 'column-iFoaV'];
        $this->rowRules = ['row-JKEai', 'row-jwYPA', 'row-yzFTL'];
        $this->dynamicRowsEnabled = false;
    }

    public function testCorrectlyParsesTheValuesAndReturnsAnArray(): void
    {
        $this->values = [
            'A1' => 'a1', 'B1' => 'b1', 'C1' => 'c1', 'D1' => 'd1',
            'A2' => 'a2', 'B2' => 'b2', 'C2' => 'c2', 'D2' => 'd2',
            'A3' => 'a3', 'B3' => 'b3', 'C3' => 'c3', 'D3' => 'd3',
        ];

        $this->createRequest();

        $result = $this->parseTableFieldApi->parseTableField();

        $this->assertEquals([
            'dynamicRows' => [],
            'values' => [
                'row-JKEai' => ['column-aTlqZ' => 'a1', 'column-pWPaS' => 'b1', 'column-ZxYfv' => 'c1', 'column-iFoaV' => 'd1'],
                'row-jwYPA' => ['column-aTlqZ' => 'a2', 'column-pWPaS' => 'b2', 'column-ZxYfv' => 'c2', 'column-iFoaV' => 'd2'],
                'row-yzFTL' => ['column-aTlqZ' => 'a3', 'column-pWPaS' => 'b3', 'column-ZxYfv' => 'c3', 'column-iFoaV' => 'd3'],
            ]], $result);
    }

    public function testCorrectlyParsesTheValuesAndReturnsAnArrayWithDynamicRows(): void
    {
        $this->values = [
            'A1' => 'a1', 'B1' => 'b1', 'C1' => 'c1', 'D1' => 'd1',
            'A2' => 'a2', 'B2' => 'b2', 'C2' => 'c2', 'D2' => 'd2',
            'A3' => 'a3', 'B3' => 'b3', 'C3' => 'c3', 'D3' => 'd3',
            'A4' => 'a4', 'B4' => 'b4', 'C4' => 'c4', 'D4' => 'd4',
            'A5' => 'a5', 'B5' => 'b5', 'C5' => 'c5', 'D5' => 'd5',
        ];

        $this->dynamicRowsEnabled = true;
        $this->createRequest();

        $result = $this->parseTableFieldApi->parseTableField();

        // Get the Dynamic row slugs from the result
        [$rowNumberFourSlug, $rowNumberFiveSlug] = $result['dynamicRows'];

        $this->assertEquals([
            'dynamicRows' => [$rowNumberFourSlug, $rowNumberFiveSlug],
            'values' => [
                'row-JKEai' => ['column-aTlqZ' => 'a1', 'column-pWPaS' => 'b1', 'column-ZxYfv' => 'c1', 'column-iFoaV' => 'd1'],
                'row-jwYPA' => ['column-aTlqZ' => 'a2', 'column-pWPaS' => 'b2', 'column-ZxYfv' => 'c2', 'column-iFoaV' => 'd2'],
                'row-yzFTL' => ['column-aTlqZ' => 'a3', 'column-pWPaS' => 'b3', 'column-ZxYfv' => 'c3', 'column-iFoaV' => 'd3'],
                $rowNumberFourSlug => ['column-aTlqZ' => 'a4', 'column-pWPaS' => 'b4', 'column-ZxYfv' => 'c4', 'column-iFoaV' => 'd4'],
                $rowNumberFiveSlug => ['column-aTlqZ' => 'a5', 'column-pWPaS' => 'b5', 'column-ZxYfv' => 'c5', 'column-iFoaV' => 'd5'],
            ]], $result);
    }

    public function testCorrectlyParsesTheValuesAndReturnsAnArrayWithDynamicRowsAndEmptyValues(): void
    {
        $this->values = [
            'A1' => 'a1', 'B1' => 'b1', 'C1' => 'c1', 'D1' => 'd1',
            'A2' => 'a2', 'B2' => 'b2', 'C2' => 'c2', 'D2' => 'd2',
            'A3' => 'a3', 'B3' => 'b3', 'C3' => 'c3', 'D3' => 'd3',
            'A9' => 'a9', 'B9' => 'b9', 'C9' => 'c9', 'D9' => 'd9',
        ];

        $this->dynamicRowsEnabled = true;
        $this->createRequest();

        $result = $this->parseTableFieldApi->parseTableField();

        // Get the Dynamic row slugs from the result
        [$rowFourSlug, $rowFiveSlug, $rowSixSlug, $rowSevenSlug, $rowEightSlug, $rowNineSlug] = $result['dynamicRows'];

        $this->assertEquals([
            'dynamicRows' => [$rowFourSlug, $rowFiveSlug, $rowSixSlug, $rowSevenSlug, $rowEightSlug, $rowNineSlug],
            'values' => [
                'row-JKEai' => ['column-aTlqZ' => 'a1', 'column-pWPaS' => 'b1', 'column-ZxYfv' => 'c1', 'column-iFoaV' => 'd1'],
                'row-jwYPA' => ['column-aTlqZ' => 'a2', 'column-pWPaS' => 'b2', 'column-ZxYfv' => 'c2', 'column-iFoaV' => 'd2'],
                'row-yzFTL' => ['column-aTlqZ' => 'a3', 'column-pWPaS' => 'b3', 'column-ZxYfv' => 'c3', 'column-iFoaV' => 'd3'],
                $rowNineSlug => ['column-aTlqZ' => 'a9', 'column-pWPaS' => 'b9', 'column-ZxYfv' => 'c9', 'column-iFoaV' => 'd9'],
            ]], $result);
    }

    public function testItHandlesAnyColumnCanBeLabelOnDynamicRows(): void
    {
        // 'column-aTlqZ' is a label column 'A'
        $this->filters[] = new Filter(['type' => Filter::TYPE_LABEL], 'column-aTlqZ');
        // 'column-pWPaS' is a label column 'B'
        $this->filters[] = new Filter(['type' => Filter::TYPE_LABEL], 'column-pWPaS');

        $this->values = [
            'C1' => 'c1', 'D1' => 'd1',
            'C2' => 'c2', 'D2' => 'd2',
            'C5' => 'c5', 'D5' => 'd5',
        ];

        $this->dynamicRowsEnabled = true;

        $this->createRequest();

        $result = $this->parseTableFieldApi->parseTableField();

        // Get the Dynamic row slugs from the result
        [$rowFourSlug, $rowFiveSlug] = $result['dynamicRows'];

        // As Column 'A' and 'B' are reserved for label it should add column 'C' is as empty string 'column-ZxYfv'
        $this->assertEquals([
            'dynamicRows' => [$rowFourSlug, $rowFiveSlug],
            'values' => [
                'row-JKEai' => ['column-ZxYfv' => 'c1', 'column-iFoaV' => 'd1'],
                'row-jwYPA' => ['column-ZxYfv' => 'c2', 'column-iFoaV' => 'd2'],
                $rowFiveSlug => ['column-ZxYfv' => 'c5', 'column-iFoaV' => 'd5'],
            ]], $result);
    }

    public function testItHandlesEmptyArray(): void
    {
        $this->values = [];

        $this->createRequest();

        $result = $this->parseTableFieldApi->parseTableField();

        $this->assertEquals(['dynamicRows' => [], 'values' => []], $result);
    }

    public function testItHandlesEmptyString(): void
    {
        $this->values = '';

        $this->createRequest();

        $result = $this->parseTableFieldApi->parseTableField();

        $this->assertEquals(['dynamicRows' => [], 'values' => []], $result);
    }

    public function testThrowsValidationExceptionIfOneOfTheValuesDoNotSpecificTheRowNumber(): void
    {
        $this->values = [
            'A1' => 'a1', 'B1' => 'b1', 'C1' => 'c1', 'D1' => 'd1',
            'A2' => 'a2', 'B2' => 'b2', 'C2' => 'c2', 'c' => 'd2', // Missing row number
            'A3' => 'a3', 'B3' => 'b3', 'C3' => 'c3', 'D3' => 'd3',
        ];

        try {
            $this->createRequest();

            $this->parseTableFieldApi->parseTableField();
        } catch (ValidationException $e) {
            $this->assertEquals('Invalid row c.', $e->errors()[$this->fieldName][0]);
        }
    }

    public function testThrowsValidationExceptionIfOneOfTheValuesDoNotSpecificTheColumnString(): void
    {
        $this->values = [
            'A1' => 'a1', 'B1' => 'b1', 'C1' => 'c1', 'D1' => 'd1',
            'A2' => 'a2', 'B2' => 'b2', 'C2' => 'c2', '2' => 'd2', // Missing column string
            'A3' => 'a3', 'B3' => 'b3', 'C3' => 'c3', 'D3' => 'd3',
        ];

        try {
            $this->createRequest();

            $this->parseTableFieldApi->parseTableField();
        } catch (ValidationException $e) {
            $this->assertEquals('Invalid column 2.', $e->errors()[$this->fieldName][0]);
        }
    }

    public function testThrowsValidationExceptionIfDynamicRowsEnabledAndRowNumberGreaterThanNumberOfRowSlugs(): void
    {
        $this->values = [
            'A1' => 'a1', 'B1' => 'b1', 'C1' => 'c1', 'D1' => 'd1',
            'A2' => 'a2', 'B2' => 'b2', 'C2' => 'c2', 'D2' => 'd2',
            'A3' => 'a3', 'B3' => 'b3', 'C3' => 'c3', 'D3' => 'd3',
            'A4' => 'a4', 'B4' => 'b4', 'C4' => 'c4', 'D4' => 'd4', //extra row
        ];

        try {
            $this->createRequest();

            $this->parseTableFieldApi->parseTableField();
        } catch (ValidationException $e) {
            $this->assertEquals('The number of rows exceeds the maximum allowed.', $e->errors()[$this->fieldName][0]);
        }
    }

    public function testThrowsValidationExceptionIfColumnsGoesBeyondColumZ(): void
    {
        // Adds rule that accepts column AA
        $slug = substr(Slug::random(), 0, 5);
        $this->columnRules = array_map(static fn($item) => $slug, range(1, 27));

        $this->values = [
            'A1' => 'a1', 'B1' => 'b1', 'C1' => 'c1', 'D1' => 'd1',
            'A2' => 'a2', 'B2' => 'b2', 'C2' => 'c2', 'D2' => 'd2',
            'A3' => 'a3', 'B3' => 'b3', 'C3' => 'c3', 'AA3' => 'd3',
        ];

        try {
            $this->createRequest();

            $this->parseTableFieldApi->parseTableField();
        } catch (ValidationException $e) {
            $this->assertEquals('The API can only handle columns A-Z.', $e->errors()[$this->fieldName][0]);
        }
    }

    public function testThrowsValidationExceptionIfItTryToDefineAnLabelColumn(): void
    {
        // 'column-aTlqZ' is a label column 'A'
        $this->filters[] = new Filter(['type' => Filter::TYPE_LABEL], 'column-aTlqZ');

        $this->values = [
            'A1' => 'a1', 'B1' => 'b1', 'C1' => 'c1', 'D1' => 'd1',
            'A2' => 'a2', 'B2' => 'b2', 'C2' => 'c2', 'D2' => 'd2',
            'A3' => 'a3', 'B3' => 'b3', 'C3' => 'c3', 'D3' => 'd3',
        ];

        try {
            $this->createRequest();

            $this->parseTableFieldApi->parseTableField();
        } catch (ValidationException $e) {
            $this->assertEquals('The API can not handle label columns.', $e->errors()[$this->fieldName][0]);
        }
    }

    public function testItCanHandleMultiCellsWithTheSameValue(): void
    {
        $this->values = [
            'A1' => 'same value', 'B1' => 'same value', 'C1' => 'same value', 'D1' => 'same value',
            'A2' => 'same value', 'B2' => 'same value', 'C2' => 'same value', 'D2' => 'same value',
            'A3' => 'same value', 'B3' => 'same value', 'C3' => 'same value', 'D3' => 'same value',
        ];

        $this->createRequest();

        $result = $this->parseTableFieldApi->parseTableField();

        $this->assertEquals([
            'dynamicRows' => [],
            'values' => [
                'row-JKEai' => ['column-aTlqZ' => 'same value', 'column-pWPaS' => 'same value', 'column-ZxYfv' => 'same value', 'column-iFoaV' => 'same value'],
                'row-jwYPA' => ['column-aTlqZ' => 'same value', 'column-pWPaS' => 'same value', 'column-ZxYfv' => 'same value', 'column-iFoaV' => 'same value'],
                'row-yzFTL' => ['column-aTlqZ' => 'same value', 'column-pWPaS' => 'same value', 'column-ZxYfv' => 'same value', 'column-iFoaV' => 'same value'],
            ]], $result);
    }

    public function createRequest(): void
    {
        $fieldAttributes = [
            'type' => Field::TYPE_TABLE,
            'configuration' => json_encode(
                [
                    'columns' => $this->columnRules,
                    'rows' => $this->rowRules,
                    'filters' => $this->filters,
                    'dynamicRowsEnabled' => $this->dynamicRowsEnabled],
                JSON_THROW_ON_ERROR
            ),
        ];

        $field = $this->muffin(Field::class, $fieldAttributes);

        $this->fieldName = Vertical::replace('entry_fields').'.'.$field->slug;

        $rule = new TableFieldValidator($field);

        $this->parseTableFieldApi = new ParseTableFieldApi($this->fieldName, $this->values, $rule);
    }
}
