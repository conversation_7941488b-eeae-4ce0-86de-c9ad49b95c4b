<?php

namespace Tests\Http\Requests\Authentication;

use AwardForce\Http\Requests\Authentication\AuthenticateTokenRequest;
use Platform\Features\Feature;
use Platform\Features\Features;
use Tests\IntegratedTestCase;

final class AuthenticateTokenRequestTest extends IntegratedTestCase
{
    public function testAuthorizeEnabledApi(): void
    {
        $request = new AuthenticateTokenRequest;
        current_account()->defineFeatures(new Features([
            new Feature('api', 'enabled'),
        ]));

        $this->assertTrue($request->authorize());
    }

    public function testNotAuthorizeDisabledApi(): void
    {
        $request = new AuthenticateTokenRequest;
        current_account()->defineFeatures(new Features([
            new Feature('api', 'disabled'),
        ]));

        $this->assertFalse($request->authorize());
    }
}
