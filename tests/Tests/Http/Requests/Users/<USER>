<?php

namespace Tests\Http\Requests\Users;

use AwardForce\Http\Requests\User\InviteUserRequest;
use AwardForce\Modules\Accounts\Contracts\MembershipRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Roles\ValueObjects\Mode;
use Eloquence\Behaviours\Slug;
use Illuminate\Support\Facades\Auth;
use Mockery as m;
use Tests\Acceptance\Models\User;

trait InviteRequestMocks
{
    protected InviteUserRequest $request;

    /** @var RoleRepository */
    protected $roles;

    /** @var array */
    protected $allRoles;

    /** @var User */
    protected $user;

    protected MembershipRepository $memberships;

    public function initMocks()
    {
        $this->user = m::mock(User::class);

        $this->allRoles = [
            $guest = $this->muffin(Role::class, ['guest' => true]),
            $role1 = $this->muffin(Role::class),
            $role2 = $this->muffin(Role::class),
            $role3 = $this->muffin(Role::class),
        ];

        $guest->slug = new Slug('guest');
        $role1->slug = new Slug('$role1');
        $role2->slug = new Slug('$role2');
        $role3->slug = new Slug('$role3');

        $role1->permissions()->create(['resource' => 'Entries', 'action' => 'create', 'mode' => new Mode('allow')]);

        $role2->permissions()->create(['resource' => 'Entries', 'action' => 'delete', 'mode' => new Mode('allow')]);
        $role2->permissions()->create(['resource' => 'Entries', 'action' => 'update', 'mode' => new Mode('allow')]);

        $role3->permissions()->create(['resource' => 'Entries', 'action' => 'delete', 'mode' => new Mode('allow')]);
        $role3->permissions()->create(['resource' => 'Entries', 'action' => 'update', 'mode' => new Mode('allow')]);
        $role3->permissions()->create(['resource' => 'Entries', 'action' => 'create', 'mode' => new Mode('allow')]);

        $this->request = new InviteUserRequest;

        $this->roles = m::mock(RoleRepository::class);
        app()->instance(RoleRepository::class, $this->roles);

        $this->memberships = m::mock(MembershipRepository::class);
        app()->instance(MembershipRepository::class, $this->memberships);

        Auth::shouldReceive('user')->andReturn($this->user);
    }
}
