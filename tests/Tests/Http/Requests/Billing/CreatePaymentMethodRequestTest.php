<?php

namespace Tests\Http\Requests\Billing;

use AwardForce\Http\Requests\Billing\CreatePaymentMethodRequest;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\IntegratedTestCase;
use Validator;

final class CreatePaymentMethodRequestTest extends IntegratedTestCase
{
    use WithFaker;

    protected $request;

    public function init()
    {
        $this->request = new CreatePaymentMethodRequest();
    }

    public function testValidationPasses(): void
    {
        $this->request->replace([
            'paymentIntentId' => $this->faker->randomNumber(),
            'customerId' => $this->faker->randomNumber(),
        ]);

        $validator = Validator::make($this->request->all(), $this->request->rules());

        $this->assertTrue($validator->passes());
        $this->assertTrue($validator->errors()->isEmpty());
    }

    public function testPaymentIntentValidationFails(): void
    {
        $this->request->replace([
            'customerId' => $this->faker->randomNumber(),
        ]);

        $validator = Validator::make($this->request->all(), $this->request->rules());

        $this->assertFalse($validator->passes());
        $this->assertFalse($validator->errors()->isEmpty());
    }

    public function testCustomerIdValidationFails(): void
    {
        $this->request->replace([
            'paymentIntentId' => $this->faker->randomNumber(),
        ]);

        $validator = Validator::make($this->request->all(), $this->request->rules());

        $this->assertFalse($validator->passes());
        $this->assertFalse($validator->errors()->isEmpty());
    }
}
