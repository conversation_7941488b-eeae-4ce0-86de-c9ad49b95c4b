<?php

namespace Tests\Library\Search\Enhancers;

use AwardForce\Library\Search\Enhancers\RemoveDuplicates;
use Illuminate\Support\Collection;
use Illuminate\Support\Fluent;
use Platform\Search\Defaults;
use Tests\UnitTestCase;

final class RemoveDuplicatesTest extends UnitTestCase
{
    public function testRemovesDuplicatesFromExport(): void
    {
        $results = $this->generate(0, 10)->merge($this->generate(1, 10))->merge($this->generate(2, 10))->shuffle();

        $enhancer = new RemoveDuplicates('id', new Defaults('export'));

        $this->assertTrue($enhancer->applies());

        $enhancer->enhance($results);
        $results = $results->just('id');

        $this->assertCount(3, $results);
        $this->assertContains(0, $results);
        $this->assertContains(1, $results);
        $this->assertContains(2, $results);
        $this->assertTrue(app(RemoveDuplicates::WARNING_KEY));
    }

    public function testDoesNotWarnAboutExportsWithoutDuplicates(): void
    {
        $results = $this->generate(0, 1)->merge($this->generate(1, 1))->merge($this->generate(2, 1))->shuffle();

        $enhancer = new RemoveDuplicates('id', new Defaults('export'));

        $this->assertTrue($enhancer->applies());

        $enhancer->enhance($results);
        $results = $results->just('id');

        $this->assertCount(3, $results);
        $this->assertFalse(app()->has(RemoveDuplicates::WARNING_KEY));
    }

    /**
     * Duplicates are not possible for list views.
     */
    public function testDoesNotApplyToSearch(): void
    {
        $enhancer = new RemoveDuplicates('id', new Defaults('search'));

        $this->assertFalse($enhancer->applies());
    }

    private function generate(int $id, int $times)
    {
        return Collection::times($times, function () use ($id) {
            return new Fluent(['id' => $id]);
        });
    }
}
