<?php

namespace Tests\Library\Html;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Awards\Data\Award;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Funding\Data\Fund;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Panels\Models\Panel;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Seasons\Models\Season;
use Eloquence\Behaviours\Slug;
use HTML;
use Platform\Language\Language;
use Tests\IntegratedTestCase;

final class LinksTest extends IntegratedTestCase
{
    protected $deletedSpan = '<span class="deleted">';

    /** @var Chapter */
    protected $chapter;

    public function init()
    {
        $this->chapter = $this->muffin(Chapter::class);
    }

    protected function setConsumer($canUpdate = true, $resource = 'Chapters', $operation = 'update')
    {
        Consumer::shouldReceive('can')->with($operation, $resource)->andReturn($canUpdate);
        Consumer::shouldReceive('get')->andReturn($this->consumer());
        Consumer::shouldReceive('language')->andReturn(new Language('en_GB'));
        Consumer::shouldReceive('languageCode')->andReturn(new Language('en_GB'));
        Consumer::shouldReceive('usesDefaultLanguage')->andReturn(true);
        Consumer::shouldReceive('isUser')->andReturn(true);
        Consumer::shouldReceive('isProgramManager')->andReturn(true);
    }

    public function testChapterLinkCanUpdate(): void
    {
        $this->setConsumer(true, 'Chapters');
        $chapter = $this->chapter;
        $html = HTML::chapterLink($chapter)->toHtml();

        $this->assertStringContainsString((string) $chapter->slug, $html);
    }

    public function testChapterLinkCanNotUpdate(): void
    {
        $this->setConsumer(false, 'Chapters');
        $chapter = $this->chapter;
        $this->assertEquals($chapter->name, HTML::chapterLink($chapter));
    }

    public function testChapterDeletedLink(): void
    {
        $chapter = $this->chapter;
        $chapter->delete();

        $html = HTML::chapterLink($chapter)->toHtml();
        $this->assertStringContainsString($this->deletedSpan, $html);
    }

    public function testNoChapterLink(): void
    {
        $this->assertEquals('', HTML::chapterLink(null));
    }

    public function testRawResourceLinkCanUpdate(): void
    {
        $this->setConsumer(true, 'Chapters');

        /** @var Chapter $chapter */
        $chapter = $this->chapter;
        $html = HTML::rawResourceLink('chapter.edit', 'Chapters', $chapter->name, $chapter->slug, $chapter->deletedAt);

        $this->assertStringContainsString((string) $chapter->slug, $html);
    }

    public function testRawResourceLinkCanNotUpdate(): void
    {
        $this->setConsumer(false, 'Chapters');

        /** @var Chapter $chapter */
        $chapter = $this->chapter;
        $this->assertEquals(
            $chapter->name,
            HTML::rawResourceLink('chapter.edit', 'Chapters', $chapter->name, $chapter->slug, $chapter->deletedAt)
        );
    }

    public function testRawResourceNoName(): void
    {
        $chapter = $this->chapter;
        $this->assertEquals(
            trans('miscellaneous.no_translation_available'),
            HTML::rawResourceLink('chapter.edit', 'Chapters', '', $chapter->slug, $chapter->deletedAt)
        );
    }

    public function testRawResourceDeletedLink(): void
    {
        /** @var Chapter $chapter */
        $chapter = $this->chapter;
        $chapter->delete();

        $html = HTML::rawResourceLink(
            'chapter.edit',
            'Chapters',
            $chapter->name,
            $chapter->slug,
            $chapter->deletedAt
        )->toHtml();
        $this->assertStringContainsString($this->deletedSpan, $html);
    }

    public function testFundLinkCanUpdate(): void
    {
        $fund = $this->muffin(Fund::class);
        $this->setConsumer(true, 'Funding');

        $html = HTML::fundLink($fund);
        $this->assertStringContainsString((string) $fund->slug, $html);

        $fund->delete();
        $html = HTML::fundLink($fund);
        $this->assertStringContainsString($this->deletedSpan, $html);
        $this->assertEmpty(HTML::fundLink(null));
    }

    public function testFundLinkCanNotUpdate(): void
    {
        $fund = $this->muffin(Fund::class);
        $this->setConsumer(false, 'Funding');

        $html = HTML::fundLink($fund);
        $this->assertEquals($fund->name, $html);
    }

    public function testOrderLinkCanView(): void
    {
        $this->setConsumer(true, 'Orders', 'view');
        $order = $this->muffin(Order::class);

        $html = HTML::orderLink($order);
        $this->assertStringContainsString((string) $order->slug, $html);

        $order->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::orderLink($order));
        $this->assertEmpty(HTML::orderLink(null));
    }

    public function testOrderLinkCanNotView(): void
    {
        $this->setConsumer(false, 'Orders', 'view');
        $order = $this->muffin(Order::class, ['local_id' => 3, 'invoice_number' => 6]);

        $html = HTML::orderLink($order);
        $this->assertEquals(trans('payments.invoice.prefix'). 6, $html);
    }

    public function testOrderItemsLinkCanView(): void
    {
        $this->setConsumer(true, 'Orders', 'view');
        $order = $this->muffin(Order::class);

        $html = HTML::orderItemsLink($order);
        $this->assertStringContainsString((string) $order->slug, $html);

        $order->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::orderItemsLink($order));
        $this->assertEmpty(HTML::orderItemsLink(null));
    }

    public function testOrderItemsLinkCanNotView(): void
    {
        $this->setConsumer(false, 'Orders', 'view');
        $order = $this->muffin(Order::class);

        $html = HTML::orderItemsLink($order);
        $this->assertEquals('', $html);
    }

    public function testRoleLinkCanView(): void
    {
        $this->setConsumer(true, 'Roles', 'view');
        $role = $this->muffin(Role::class);

        $html = HTML::roleLink($role);
        $this->assertStringContainsString((string) $role->slug, $html);

        $role->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::roleLink($role));
        $this->assertEmpty(HTML::roleLink(null));
    }

    public function testRoleLinkCanNotView(): void
    {
        $this->setConsumer(false, 'Roles', 'view');
        /** @var Role $role */
        $role = $this->muffin(Role::class);

        $html = HTML::roleLink($role);
        $this->assertEquals(lang($role, 'name'), $html);
    }

    public function testRoundLinkCanView(): void
    {
        $this->setConsumer(true, 'Rounds', 'view');
        $round = $this->muffin(Round::class);

        $html = HTML::roundLink($round);
        $this->assertStringContainsString((string) $round->slug, $html);

        $round->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::roundLink($round));
        $this->assertEmpty(HTML::roundLink(null));
    }

    public function testRoundLinkCanNotView(): void
    {
        $this->setConsumer(false, 'Rounds', 'view');
        $round = $this->muffin(Round::class);

        $html = HTML::roundLink($round);
        $this->assertEquals(lang($round, 'name'), $html);
    }

    public function testContentBlockLinkCanUpdate(): void
    {
        $this->setConsumer(true, 'Content', 'update');
        $content = $this->muffin(ContentBlock::class);

        $html = HTML::contentBlockLink($content);
        $this->assertStringContainsString((string) $content->slug, $html);

        $content->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::contentBlockLink($content));
        $this->assertEmpty(HTML::contentBlockLink(null));
    }

    public function testContentBlockLinkCanNotUpdate(): void
    {
        $this->setConsumer(false, 'Content', 'update');
        $content = $this->muffin(ContentBlock::class);

        $html = HTML::contentBlockLink($content);
        $this->assertEquals(lang($content, 'title'), $html);
    }

    public function testSeasonLinkCanEdit(): void
    {
        $this->setConsumer(true, 'Seasons', 'update');
        $season = $this->muffin(Season::class);

        $html = HTML::seasonLink($season);
        $this->assertStringContainsString((string) $season->slug, $html);

        $season->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::seasonLink($season));

        $html = HTML::seasonLink(null);
        $this->assertStringContainsString(trans('seasons.status.destroyed'), $html);
    }

    public function testSeasonCanNotEdit(): void
    {
        $this->setConsumer(false, 'Seasons', 'update');
        $season = $this->muffin(Season::class);

        $html = HTML::seasonLink($season);
        $this->assertEquals(lang($season, 'name'), $html);
    }

    public function testCategoryLinkCanUpdate(): void
    {
        $this->setConsumer(true, 'Categories', 'update');
        $category = $this->muffin(Category::class);

        $html = HTML::categoryLink($category);
        $this->assertStringContainsString((string) $category->slug, $html);

        $category->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::categoryLink($category));
        $this->assertEmpty(HTML::categoryLink(null));
    }

    public function testCategoryLinkCanNotUpdate(): void
    {
        $this->setConsumer(false, 'Categories', 'update');
        $category = $this->muffin(Category::class);

        $html = HTML::categoryLink($category);
        $this->assertEquals(lang($category, 'name'), $html);
    }

    public function testFieldLinkCanUpdate(): void
    {
        $this->setConsumer(true, 'Fields', 'update');
        $field = $this->muffin(Field::class);

        $html = HTML::fieldLink($field);
        $this->assertStringContainsString((string) $field->slug, $html);

        $field->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::fieldLink($field));
        $this->assertEmpty(HTML::fieldLink(null));
    }

    public function testFieldLinkCanNotUpdate(): void
    {
        $this->setConsumer(false, 'Fields', 'update');
        $field = $this->muffin(Field::class);

        $html = HTML::fieldLink($field);
        $this->assertEquals(lang($field, 'title'), $html);
    }

    public function testScoreSetLink(): void
    {
        $scoreSet = $this->muffin(ScoreSet::class);
        $html = HTML::scoreSetLink($scoreSet);
        $this->assertStringContainsString($scoreSet->name, $html);

        $this->setConsumer(true, 'ScoreSets', 'update');
        $html = HTML::scoreSetLink($scoreSet);
        $this->assertStringContainsString((string) $scoreSet->slug, $html);

        $scoreSet->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::scoreSetLink($scoreSet));
        $this->assertEmpty(HTML::scoreSetLink(null));
    }

    public function testScoreSetIndexLink(): void
    {
        $scoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_QUALIFYING]);

        $html = HTML::scoreSetIndexLink($scoreSet);
        $this->assertStringContainsString((string) $scoreSet->slug, $html);
    }

    public function testPanelLink(): void
    {
        $panel = $this->muffin(Panel::class);
        $html = HTML::panelLink($panel);
        $this->assertStringContainsString($panel->name, $html);
        $this->setConsumer(true, 'Panels', 'update');

        $html = HTML::panelLink($panel);
        $this->assertStringContainsString((string) $panel->slug, $html);

        $panel->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::panelLink($panel));
        $this->assertEmpty(HTML::panelLink(null));
    }

    public function testReviewStageLink(): void
    {
        $review = $this->muffin(ReviewStage::class);
        $html = HTML::reviewStageLink($review);
        $this->assertStringContainsString($review->name, $html);

        $this->setConsumer(true, 'Settings', 'update');

        $html = HTML::reviewStageLink($review);
        $this->assertStringContainsString((string) $review->slug, $html);
    }

    public function testPanelLinks(): void
    {
        $this->setConsumer(true, 'Panels', 'update');
        $panels = [
            $panel1 = $this->muffin(Panel::class),
            $panel2 = $this->muffin(Panel::class),
        ];

        $html = HTML::panelLinks([$panel1->id, $panel2->id], collect($panels));
        $this->assertStringContainsString($panel1->slug, $html);
        $this->assertStringContainsString($panel2->slug, $html);
    }

    public function testEntryLink(): void
    {
        $this->setConsumer(true, 'Entries', 'update');
        $entry = $this->muffin(Entry::class);

        $html = HTML::entryLink($entry);
        $this->assertStringContainsString((string) $entry->slug, $html);

        $this->setConsumer(false, 'Entries', 'update');
        $html = HTML::entryLink($entry);
        $this->assertStringContainsString($entry->title, $html);

        $entry->delete();
        $this->assertStringContainsString($this->deletedSpan, HTML::entryLink($entry));
        $this->assertStringContainsString(trans('entries.status.destroyed'), HTML::entryLink(null));
    }

    public function testEntryLinks(): void
    {
        $this->setConsumer(true, 'Entries', 'update');
        $entries = [
            $entry1 = $this->muffin(Entry::class),
            $entry2 = $this->muffin(Entry::class),
        ];

        $html = HTML::entryLinks([$entry1->id, $entry2->id], collect($entries));
        $this->assertStringContainsString((string) $entry1->slug, $html);
        $this->assertStringContainsString((string) $entry2->slug, $html);
    }

    public function testContactLink(): void
    {
        /** @var User $user */
        $user = $this->muffin(User::class);
        $user->email = '<EMAIL>';

        $html = HTML::contactLink($user);
        $this->assertStringContainsString($email = $user->email, $html);

        $user->email = null;
        $user->mobile = '12345';

        $html = HTML::contactLink($user);
        $this->assertStringContainsString($user->mobile, $html);
        $this->assertStringNotContainsString($email, $html);

        $user->email = "test'<EMAIL>";
        $html = HTML::contactLink($user);

        $this->assertEquals('<a href="mailto:test\'<EMAIL>">test\'<EMAIL></a>', $html);
    }

    public function testTabName(): void
    {
        $tab = $this->muffin(Tab::class);

        $html = HTML::tabName($tab);
        $this->assertStringContainsString($tab->name, $html);

        $tab->delete();
        $html = HTML::tabName($tab);
        $this->assertStringContainsString($this->deletedSpan, $html);

        $this->assertEquals(HTML::tabName(null), '-');
    }

    public function testTabLink(): void
    {
        $tab = $this->muffin(Tab::class);

        $html = HTML::tabLink($tab);
        $this->assertStringContainsString((string) $tab->slug, $html);

        $tab->delete();
        $html = HTML::tabLink($tab);
        $this->assertStringContainsString($this->deletedSpan, $html);

        $this->assertEmpty(HTML::tabLink(null));
    }

    public function testDuplicateLink(): void
    {
        $entry1 = $this->muffin(Entry::class);
        $entry2 = $this->muffin(Entry::class);

        $html = HTML::duplicateLink($entry1, $entry1);
        $this->assertStringContainsString(trans('entries.duplicates.panel.this_entry'), $html);

        $html = HTML::duplicateLink($entry1, $entry2);
        $this->assertStringContainsString((string) $entry1->slug, $html);

        $entry1->delete();
        $html = HTML::duplicateLink($entry1, $entry2);
        $this->assertStringContainsString($this->deletedSpan, $html);
    }

    public function testAwardLink(): void
    {
        $entry = $this->muffin(Entry::class);
        $award = $this->muffin(Award::class);

        $html = HTML::awardLink($award, $entry);
        $this->assertStringContainsString((string) $award->slug, $html);
        $this->assertStringContainsString((string) $entry->slug, $html);
    }

    public function testTotalEntriesLink(): void
    {
        $total = 456;
        $judge = new User;
        $judge->id = 2;
        $judge->slug = Slug::random();
        $scoreSet = new ScoreSet;
        $scoreSet->id = 3;

        $html = HTML::totalEntriesLink($total, $judge, $scoreSet);

        $this->assertStringNotContainsString($judge->slug, $html);
        $this->assertStringNotContainsString($scoreSet->id, $html);
        $this->assertStringContainsString($total, $html);

        $this->setConsumer(true, 'EntriesAll', 'view');
        $html = HTML::totalEntriesLink($total, $judge, $scoreSet);
        $this->assertStringContainsString('judge', $html);
        $this->assertStringContainsString($judge->slug, $html);
        $this->assertStringContainsString($scoreSet->id, $html);
        $this->assertStringContainsString($total, $html);

        current_account()->vertical = 'grants';
        current_account()->brand = Account::BRAND_GOODGRANTS;
        current_account()->save();

        $this->assertStringContainsString('reviewer='.$judge->slug, HTML::totalEntriesLink($total, $judge, $scoreSet));
    }
}
