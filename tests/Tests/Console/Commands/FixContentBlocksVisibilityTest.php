<?php

namespace Tests\Console\Commands;

use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use Illuminate\Support\Facades\Artisan;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class FixContentBlocksVisibilityTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testShouldOnlyChangeStandardContentBlocks(): void
    {
        $standardContentType = $this->muffin(ContentBlock::class, [
            'key' => 'home-program-description',
            'visibility' => 'initially',
        ]);
        $infoBoxContentType = $this->muffin(ContentBlock::class, [
            'key' => 'entrant-home',
            'visibility' => 'initially',
        ]);
        $contenBlocks = app(ContentBlockRepository::class)->getAll();

        $this->assertEquals('initially', $contenBlocks->where('id', $standardContentType->id)->first()->visibility);
        $this->assertEquals('initially', $contenBlocks->where('id', $infoBoxContentType->id)->first()->visibility);

        Artisan::call('dev:fix-content-blocks-visibility');
        $contenBlocks = app(ContentBlockRepository::class)->getAll();

        $this->assertEquals('open', $contenBlocks->where('id', $standardContentType->id)->first()->visibility);
        $this->assertEquals('initially', $contenBlocks->where('id', $infoBoxContentType->id)->first()->visibility);
    }
}
