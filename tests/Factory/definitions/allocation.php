<?php

use AwardForce\Library\Values\Amount;
use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\Fund;
use League\FactoryMuffin\FactoryMuffin;

$allocated = 0;
/** @var FactoryMuffin $this */
$this->define(Allocation::class)
    ->setDefinitions([
        'account_id' => function () {
            return current_account_id();
        },
        'fund_id' => 'factory|'.Fund::class,
        'entry_id' => 'factory|'.Entry::class,
        'season_id' => function () {
            return current_account()->activeSeason()->id;
        },
        'amount' => function () use (&$allocated) {
            return new Amount($allocated = random_int(0, 100000), new Currency('AUD'));
        },
    ]);
