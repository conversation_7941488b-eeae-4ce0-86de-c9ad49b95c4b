<?php

use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Entries\Models\Contract;
use AwardForce\Modules\Entries\Models\Entry;
use Faker\Factory as Faker;
use League\FactoryMuffin\FactoryMuffin;

/** @var FactoryMuffin $this */
$this->define(Contract::class)
    ->setDefinitions([
        'account_id' => function () {
            return current_account_id();
        },
        'entry_id' => 'factory|'.Entry::class,
        'content_block_id' => 'factory|'.ContentBlock::class,
        'signed_at' => function () {
            return null;
        },
        'title' => fn() => Faker::create()->catchPhrase(),
    ]);
