<?php

namespace Features\Setup\Contexts;

use AwardForce\Modules\Accounts\Services\AccountGateway;
use AwardForce\Modules\Api\V2\Models\ApiKey;
use Illuminate\Support\Str;
use Mo<PERSON>y as m;

trait ApiKeysFeatures
{
    /** @var array */
    private $apiKeys;

    /**
     * @Given a number of API keys exist
     */
    public function numberOfAPIKeysExist()
    {
        $numOfRecords = mt_rand(1, 3);

        for ($i = 0; $i < $numOfRecords; $i++) {
            $this->apiKeys[] = $this->muffin(ApiKey::class, ['name' => 'ApiKeyName'.$i]);
        }
    }

    /**
     * @Then I should see a list of API keys
     */
    public function iShouldSeeAListOfAPIKeys()
    {
        $this->route('GET', 'api-key.index');

        $this->assertResponseOk();
    }

    /**
     * @Then I should not see a list of API keys
     */
    public function iShouldNotSeeAListOfAPIKeys()
    {
        $this->route('GET', 'api-key.index');

        $this->assertResponseStatus(403);
    }

    /**
     * @Given I want to generate an API key
     */
    public function iWantToGenerateAnAPIKey()
    {
        $this->route('GET', 'api-key.new');

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to generate that API key
     */
    public function iShouldBeAbleToGenerateThatAPIKey()
    {
        $gateway = m::mock(AccountGateway::class);
        $gateway->shouldReceive('addApiKey')->with((string) current_account()->globalId)->andReturn(Str::random(8));
        app()->instance(AccountGateway::class, $gateway);

        $data = [
            'account_id' => current_account()->id,
            'name' => 'Test API key name',
            'value' => Str::random(60),
            'scope' => 'read',
            'notificationEmails' => '<EMAIL>',
        ];

        $this->route('POST', 'api-key.generate', [], $this->withInput($data));

        $this->assertRedirectedToRoute('api-key.index');
    }

    /**
     * @Then I should not be able to generate that API key
     */
    public function iShouldNotBeAbleToGenerateThatAPIKey()
    {
        $this->route('POST', 'api-key.generate', [], $this->withInput([]));

        $this->assertResponseStatus(302);
        $this->assertSessionHasErrors(['error' => 'You do not have the right permissions to perform this action.']);
    }

    /**
     * @Given I can edit an API key
     */
    public function iCanEditAnAPIKey()
    {
        $this->route('GET', 'api-key.index', [$this->apiKeys[0]->slug]);

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to update that API key
     */
    public function iShouldBeAbleToUpdateThatAPIKey()
    {
        $data = [
            'name' => 'New API key name',
            'scope' => $this->apiKeys[0]->scope === 'read' ? 'write' : 'read',
            'notificationEmails' => '<EMAIL>',
        ];

        $this->route('PUT', 'api-key.update', ['apiKey' => $this->apiKeys[0]->slug], $data);

        $this->assertRedirectedToRoute('api-key.index');
    }

    /**
     * @Then I should not be able to update that API key
     */
    public function iShouldBeNotAbleToUpdateThatAPIKey()
    {
        $data = [
            'name' => 'New API key name',
            'scope' => $this->apiKeys[0]->scope === 'read' ? 'write' : 'read',
            'notificationEmails' => '<EMAIL>',
        ];

        $this->route('PUT', 'api-key.update', ['apiKey' => $this->apiKeys[0]->slug], $data);

        $this->assertResponseStatus(302);
        $this->assertSessionHasErrors(['error' => 'You do not have the right permissions to perform this action.']);
    }

    /**
     * @Then I should be able to revoke a specific API key
     */
    public function iShouldBeAbleToRevokeASpecificAPIKey()
    {
        $apiKey = $this->apiKeys[0];

        $gateway = m::mock(AccountGateway::class);
        $gateway->shouldReceive('removeApiKey')->with((string) $apiKey->slug);
        app()->instance(AccountGateway::class, $gateway);

        $this->route('DELETE', 'api-key.revoke', [$apiKey->slug], $this->withInput(['selected' => [$apiKey->id]]));
        $this->assertRedirectedToRoute('api-key.index');

        $this->route('GET', 'api-key.index');
        $this->assertResponseOk();
        $this->assertResponseNotContains((string) $apiKey->name);
    }

    /**
     * @Then I should not be able to revoke a specific API key
     */
    public function iShouldNotBeAbleToRevokeASpecificAPIKey()
    {
        $apiKey = $this->apiKeys[0];

        $this->route('DELETE', 'api-key.revoke', [$apiKey->slug], $this->withInput(['selected' => [$apiKey->id]]));

        $this->assertResponseStatus(403);
    }

    /**
     * @Then I should see the screen reader text for the Api Key
     */
    public function iShouldSeeTheScreeReaderTextForTheApiKey()
    {
        $this->assertResponseContains(trans('buttons.action_overflow', ['resource' => $this->apiKeys[0]->resourceLabel()]));
    }
}
