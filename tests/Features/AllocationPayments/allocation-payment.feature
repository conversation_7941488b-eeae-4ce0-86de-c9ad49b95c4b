Feature: Allocation payments

  Background:
    Given I am currently logged in as an "Program Manager"

  Scenario: Cannot view allocation payments page if fund management feature is disabled
    Given the fund_management feature is disabled
    Given a number of 5 allocation payments exist
    Then I try to access the allocation payments page
    Then I should be redirected to feature fund_management disabled page

  Scenario: View existing allocation payments
    Given the fund_management feature is enabled
    Given a number of 5 allocation payments exist
    Then I should see a list of allocation payments
    And I should see the screen reader text for the Allocation Payment

  Scenario: Create allocation payment
    Given the fund_management feature is enabled
    And allocation payments count is 0
    Given I want to create an allocation payment
    Then I should get a proper allocation payment json response

  Scenario: Cannot create allocation payment when there are validation errors
    Given the fund_management feature is enabled
    And allocation payments count is 0
    Given I want to create an allocation payment with validation errors
    Then I should see validation errors

  Scenario: Update allocation payment
    Given the fund_management feature is enabled
    And allocation payments count is 0
    Given a number of 1 allocation payment exist
    And allocation payments count is 1
    Then I can edit an allocation payment
    Then I should get a proper allocation payment json response

  Scenario: Cannot create allocation payment with greater amount than allocation has
    Given the fund_management feature is enabled
    And allocation payments count is 0
    Given I want to create an allocation payment with greater amount than allocation has
    Then I should see validation errors

  Scenario: Cannot update allocation payment when there are validation errors
    Given the fund_management feature is enabled
    And allocation payments count is 0
    Given a number of 1 allocation payment exist
    And allocation payments count is 1
    Given I want to update an allocation payment with validation errors
    Then I should see validation errors

  Sc<PERSON>rio: Delete allocation payment
    Given the fund_management feature is enabled
    And allocation payments count is 0
    Given a number of 1 allocation payment exist
    And allocation payments count is 1
    Then I should be able to delete a specific allocation payment
    Then allocation payments count is 0

  Scenario: Cannot delete allocation payment if status is not scheduled
    Given the fund_management feature is enabled
    And allocation payments count is 0
    Given a number of 1 allocation payment exist
    And allocation payments count is 1
    Then I should be able to delete a specific allocation payment with status paid
    Then I should see validation errors
    Then I should be able to delete a specific allocation payment with status failed
    Then I should see validation errors
    Then I should be able to delete a specific allocation payment with status processing
    Then I should see validation errors

  Scenario: Get allocation comments
    Given the fund_management feature is enabled
    And allocation payments count is 0
    Given a number of 1 allocation payment exist
    And allocation payments count is 1
    And there are 5 comments for this allocation payment
    Then I should be able to get 5 allocation payment comments
