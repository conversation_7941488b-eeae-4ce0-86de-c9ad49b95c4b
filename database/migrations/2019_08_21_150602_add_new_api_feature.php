<?php

use AwardForce\Modules\Features\Data\Feature;
use Illuminate\Database\Migrations\Migration;
use Platform\Features\Feature as PlatformFeature;

class AddNewApiFeature extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Feature::where('feature', '=', 'api')->delete();
        Feature::add(null, new PlatformFeature('api', Feature::ENABLED));
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Feature::where('feature', '=', 'api')->delete();
    }
}
