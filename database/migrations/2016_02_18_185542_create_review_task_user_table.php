<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateReviewTaskUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('review_tasks', function (Blueprint $table) {
            $table->dropColumn('assignee_user_ids');
        });

        Schema::create('review_task_user', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('review_task_id')->index();
            $table->unsignedBigInteger('user_id')->index();
            $table->string('linked_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('review_tasks', function (Blueprint $table) {
            $table->text('assignee_user_ids')->nullable();
        });
        Schema::drop('review_task_user');
    }
}
