<?php

use Illuminate\Database\Migrations\Migration;

class AddSocialSharingToCookieClassificationToAccount extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("ALTER TABLE accounts MODIFY  COLUMN footer_uses_cookies ENUM('necessary', 'analytics', 'marketing', 'social-sharing')");
        DB::statement("ALTER TABLE accounts MODIFY  COLUMN header_uses_cookies ENUM('necessary', 'analytics', 'marketing', 'social-sharing')");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("ALTER TABLE accounts MODIFY  COLUMN footer_uses_cookies ENUM('necessary', 'analytics', 'marketing')");
        DB::statement("ALTER TABLE accounts MODIFY  COLUMN header_uses_cookies ENUM('necessary', 'analytics', 'marketing')");
    }
}
