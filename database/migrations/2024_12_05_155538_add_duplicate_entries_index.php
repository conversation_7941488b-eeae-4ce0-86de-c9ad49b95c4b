<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('duplicate_entries', function (Blueprint $table) {
            $table->index(['season_id', 'out_of_date', 'parent_id', 'lft', 'rgt', 'confirmed_at'], 'duplicate_entries_unconfirmed_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('duplicate_entries', function (Blueprint $table) {
            $table->dropIndex('duplicate_entries_index');
        });
    }
};
