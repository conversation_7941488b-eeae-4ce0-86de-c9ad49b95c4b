<?php

use AwardForce\Modules\Features\Data\Feature;
use Illuminate\Database\Migrations\Migration;
use Platform\Features\Feature as PlatformFeature;

class AddGrantReportsFeature extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $exists = Feature::where('feature', '=', 'grant_reports')->first();

        if (! $exists) {
            Feature::add(null, new PlatformFeature('grant_reports', Feature::ENABLED));
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Feature::where('feature', '=', 'grant_reports')->delete();
    }
}
