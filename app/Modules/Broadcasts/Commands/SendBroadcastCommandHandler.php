<?php

namespace AwardForce\Modules\Broadcasts\Commands;

use AwardForce\Modules\Broadcasts\Contracts\BroadcastRepository;
use AwardForce\Modules\Broadcasts\Services\BroadcastSender;
use Platform\Events\EventDispatcher;

class SendBroadcastCommandHandler
{
    use EventDispatcher;

    private $broadcasts;
    private $sender;

    public function __construct(BroadcastRepository $broadcasts, BroadcastSender $sender)
    {
        $this->broadcasts = $broadcasts;
        $this->sender = $sender;
    }

    public function handle(SendBroadcastCommand $command)
    {
        $broadcast = $command->broadcast;

        $this->sender->send($broadcast) ? $broadcast->markAsSent() : $broadcast->markAsFailed();

        $this->broadcasts->save($broadcast);

        $this->dispatch($broadcast->releaseEvents());

        return $broadcast;
    }
}
