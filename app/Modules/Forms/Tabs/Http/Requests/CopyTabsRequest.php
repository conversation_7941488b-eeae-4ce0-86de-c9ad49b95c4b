<?php

namespace AwardForce\Modules\Forms\Tabs\Http\Requests;

use AwardForce\Http\Requests\SelectedModelsRequest;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;

class CopyTabsRequest extends SelectedModelsRequest
{
    protected $repository = TabRepository::class;

    protected function getIds()
    {
        return $this->getRepository()
            ->getByIds($this->request->all()['selected'] ?? [])
            ->filter(fn(Tab $tab) => $tab->type !== Tab::TYPE_DETAILS && ! $tab->locked)
            ->pluck('id')
            ->toArray();
    }

    public function messages()
    {
        return [
            'selected.*.in' => trans('validation.details_tab', ['type' => trans('tabs.types.'.Tab::TYPE_DETAILS)]),
        ];
    }
}
