<?php

namespace AwardForce\Modules\Forms\Forms\View;

use AwardForce\Modules\Entries\View\PreviewEntry;
use AwardForce\Modules\GrantReports\Views\Entrant\PreviewEntrantGrantReport;
use AwardForce\Modules\GrantReports\Views\PreviewGrantReport;
use Illuminate\Http\Request;
use Platform\View\View;

class PreviewSubmittableFactory extends View
{
    public static function createFromRequest(Request $request): ?PreviewSubmittable
    {
        $route = $request->route()->getName();
        if (strpos($route, 'entry') !== false) {
            return app(PreviewEntry::class);
        }

        if (strpos($route, 'grant-report') !== false) {
            if (strpos($route, 'manager')) {
                return app(PreviewGrantReport::class);
            }

            return app(PreviewEntrantGrantReport::class);
        }

        return null;
    }
}
