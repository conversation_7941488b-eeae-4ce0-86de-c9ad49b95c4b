<?php

namespace AwardForce\Modules\Forms\Fields\Services;

use Illuminate\Config\Repository;
use Illuminate\Translation\Translator;
use Tests\LightUnitTestCase;

final class CompatibleTypesTest extends LightUnitTestCase
{
    /**
     * @var \Mockery\MockInterface
     */
    protected $config;

    /**
     * @var \Mockery\MockInterface
     */
    protected $translator;

    /**
     * @var CompatibleTypes
     */
    protected $service;

    public function init()
    {
        $this->translator = \Mockery::mock(Translator::class);
        $this->translator->shouldReceive('get')->andReturnUsing(function ($key) {
            return $key;
        });

        $this->config = \Mockery::mock(Repository::class);
        $this->config->shouldReceive('get')
            ->with('awardforce.fields.compatibility')
            ->andReturn([
                'date' => false,
                'time' => false,
                'email' => 'string',
                'text' => 'string',
                'textarea' => 'string',
            ]);

        $this->service = new CompatibleTypes($this->config, $this->translator);
    }

    public function testGetSortedList(): void
    {
        $this->assertEquals(['date' => 'fields.types.date'], $this->service->getSortedList('date'));

        $this->assertEquals([
            'email' => 'fields.types.email',
            'text' => 'fields.types.text',
            'textarea' => 'fields.types.textarea',
        ], $this->service->getSortedList('email'));
    }

    public function testGet(): void
    {
        $this->assertEquals(['date'], $this->service->get('date'));

        $this->assertEquals(['email', 'text', 'textarea'], $this->service->get('textarea'));
    }
}
