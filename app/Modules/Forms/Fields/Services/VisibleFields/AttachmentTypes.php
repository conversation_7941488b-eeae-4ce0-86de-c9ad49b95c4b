<?php

namespace AwardForce\Modules\Forms\Fields\Services\VisibleFields;

use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Illuminate\Support\Collection;

class AttachmentTypes
{
    /**
     * If an attachment type field is configured for the given score set, returns an array containing a single element
     * that associates the fieldId with the values that would make an attachment visible in the score set.
     *
     * @return array
     */
    public function fromScoreSet(ScoreSet $scoreSet, ?array $categoryOverrides = null)
    {
        $fieldId = $scoreSet->attachmentTypeFieldId;

        if (! empty($fieldId)) {
            $attachmentTypes = $categoryOverrides[$scoreSet->id] ?? $scoreSet->attachmentTypes;

            return [$fieldId => $this->splitTypes($attachmentTypes)];
        }

        return [];
    }

    /**
     * If an attachment type field is configured for the given review stage, returns an array containing a single element
     * that associates the fieldId with the values that would make an attachment visible in the stage.
     *
     * @return array
     */
    public function fromReviewStage(ReviewStage $reviewStage)
    {
        $fieldId = $reviewStage->attachmentTypeFieldId;

        if (! empty($fieldId)) {
            return [$fieldId => $this->splitTypes($reviewStage->attachmentTypes)];
        }

        return [];
    }

    /**
     * Combines the attachment type field setting of multiple score sets into a single array. When the same field is
     * used across multiple score sets, the values are combined.
     *
     * Example:
     *
     * scoreSet1 -> field A - values A, B
     * scoreSet2 -> field A - values C, D
     * scoreSet3 -> field B - values X, Y
     *
     * Result: [A => [A, B, C, D], B => [X, Y]]
     *
     * @return array
     */
    public function fromScoreSets(Collection $scoreSets, ?array $categoryOverrides = null)
    {
        // If there is at least one score set without an attachment type field selected, display all attachments.
        if ($scoreSets->where('attachmentTypeFieldId', null)->count()) {
            return [];
        }

        return $scoreSets->groupBy('attachmentTypeFieldId')->map(function ($scoreSets) use ($categoryOverrides) {
            $attachmentTypes = $scoreSets->map(function ($scoreSet) use ($categoryOverrides) {
                return $this->splitTypes($categoryOverrides[$scoreSet->id] ?? $scoreSet->attachmentTypes);
            });

            return $attachmentTypes->collapse()->all();
        })->all();
    }

    /**
     * Returns an array containing one element for each line in the given string, filtering out empty lines.
     */
    private function splitTypes(string $types): array
    {
        return array_filter(preg_split("#\s*\n\s*#", $types));
    }
}
