<?php

namespace AwardForce\Modules\Forms\Fields\Services;

use AwardForce\Library\Copier\Map;

class CopyExistingFormFields extends CopyExistingSeasonFields
{
    protected function getFields($original, $destination)
    {
        return $this->fields->getByFormId($original->id);
    }

    protected function seasonId($destination)
    {
        return $destination->seasonId;
    }

    protected function formId($field, $destination, Map $map)
    {
        return $destination->id;
    }
}
