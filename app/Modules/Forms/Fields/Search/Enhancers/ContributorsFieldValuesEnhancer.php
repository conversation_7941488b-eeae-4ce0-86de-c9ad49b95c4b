<?php

namespace AwardForce\Modules\Forms\Fields\Search\Enhancers;

use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\ResourceFields;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\Manager;

class ContributorsFieldValuesEnhancer extends FieldValuesEnhancer
{
    protected function visibleFields(mixed $object): Fields
    {
        $submittable = $this->forExport ? $object->submittable->fresh() : $object->submittable;
        $visibleFields = app(Manager::class)->forContributors($submittable)
            ->filter(fn(ResourceFields $resourceFields) => (string) $resourceFields->resource->slug === (string) $object->slug)
            ->first();

        return $visibleFields ? $visibleFields->fields : new Fields;
    }
}
