<?php

namespace AwardForce\Modules\Forms\Fields\Events\Listeners;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\Formula;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use Tests\IntegratedTestCase;

final class RecalculateFormulaTest extends IntegratedTestCase
{
    private $valuesService;
    private $formulaServiceMock;

    public function init()
    {
        $this->valuesService = app(ValuesService::class);
        $this->formulaServiceMock = $this->partialMock(Formula::class);
    }

    public function testFormulaIsCalculated(): void
    {
        $entry = $this->muffin(Entry::class);
        $fields = $this->muffins(2, Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'numeric']);
        $formulaField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'formula', 'configuration' => json_encode(['formula' => "=SUM({{$fields[0]->slug}}, {{$fields[1]->slug}})"])]);

        $values = [
            (string) $fields[0]->slug => 20,
            (string) $fields[1]->slug => 30,
            (string) $formulaField->slug => null,
        ];

        $this->formulaServiceMock->shouldReceive('calculate')->once()->andReturn([(string) $formulaField->slug => 50]);
        $this->valuesService->syncValuesForObject($values, $entry);

        $this->assertEquals(50, $entry->fields->where('slug', $formulaField->slug)->first()->value);
    }
}
