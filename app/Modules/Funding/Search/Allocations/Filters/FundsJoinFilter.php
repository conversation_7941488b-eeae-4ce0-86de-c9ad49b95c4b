<?php

namespace AwardForce\Modules\Funding\Search\Allocations\Filters;

use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class FundsJoinFilter implements ColumnatorFilter, SearchFilter
{
    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        return $query->join('funds', function ($join) {
            $join->on('funds.id', '=', 'fund_allocations.fund_id');
            $join->whereNull('funds.deleted_at');
        });
    }

    /**
     * Determines if this dependency applies to the search in any way.
     */
    public function applies(): bool
    {
        return true;
    }
}
