<?php

namespace AwardForce\Modules\Funding\Search;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Platform\Search\Filters\SearchFilter;

class FundFilter implements SearchFilter
{
    /**
     * @var array
     */
    private $input;

    public function __construct(array $input)
    {
        $this->input = $input;
    }

    /**
     * @param  Builder  $query
     * @return Builder
     */
    public function applyToEloquent($query)
    {
        $query->join('funds', function ($query) {
            $query->on('funds.id', '=', 'fund_allocations.fund_id')
                ->whereNull('funds.deleted_at');
        });

        if ($fundId = array_get($this->input, 'fund')) {
            $query->where('fund_allocations.fund_id', $fundId);
        }

        if (array_get($this->input, 'order') != 'fund') {
            return $query;
        }

        return $query->addSelect(DB::raw('ft.value AS ft_name'))
            ->join(DB::raw('translations ft'), function ($join) {
                $join->on('ft.foreign_id', '=', 'funds.id')
                    ->where('ft.resource', '=', 'Fund')
                    ->where('ft.field', '=', 'name');
            });
    }
}
