<?php

namespace AwardForce\Modules\Funding\Commands;

use AwardForce\Modules\Funding\Data\FundRepository;
use Platform\Events\EventDispatcher;
use Tectonic\LaravelLocalisation\Database\TranslationService;

class UpdateFundHandler
{
    use EventDispatcher;

    /** @var TranslationService */
    private $translations;

    /** @var FundRepository */
    private $funds;

    public function __construct(TranslationService $translations, FundRepository $funds)
    {
        $this->translations = $translations;
        $this->funds = $funds;
    }

    public function handle(UpdateFund $command)
    {
        $fund = $this->funds->getBySlug($command->fundSlug);
        $fund->updateBudget($command->budget);

        $this->translations->sync($fund, $command->translated);
        $fund->touch();

        $this->dispatch($fund->releaseEvents());
    }
}
