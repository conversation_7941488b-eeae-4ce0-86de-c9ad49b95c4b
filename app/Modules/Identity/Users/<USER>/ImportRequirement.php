<?php

namespace AwardForce\Modules\Identity\Users\Services;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Identity\Roles\Models\Role;

class ImportRequirement
{
    private $account;
    private $role;
    private $password;

    public function __construct(Account $account, Role $role, $password)
    {
        $this->account = $account;
        $this->role = $role;
        $this->password = $password;
    }

    /**
     * @return Account
     */
    public function account()
    {
        return $this->account;
    }

    /**
     * @return Role
     */
    public function role()
    {
        return $this->role;
    }

    /**
     * @return string
     */
    public function password()
    {
        return $this->password;
    }
}
