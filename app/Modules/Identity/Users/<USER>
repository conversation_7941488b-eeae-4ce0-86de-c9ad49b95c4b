<?php

namespace AwardForce\Modules\Identity\Users;

use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\Identity\Users\Contracts\GlobalCommunicationChannelRepository;
use AwardForce\Modules\Identity\Users\Contracts\GlobalUserRepository;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Events\EmailWasChanged;
use AwardForce\Modules\Identity\Users\Events\GlobalUserPendingUpdate;
use AwardForce\Modules\Identity\Users\Events\MobileWasChanged;
use AwardForce\Modules\Identity\Users\Events\UserWasConfirmed;
use AwardForce\Modules\Identity\Users\Events\UserWasDestroyed;
use AwardForce\Modules\Identity\Users\Listeners\GlobalUser;
use AwardForce\Modules\Identity\Users\Listeners\InvalidRecipient as InvalidRecipientListener;
use AwardForce\Modules\Identity\Users\Listeners\UserDestroyedListener;
use AwardForce\Modules\Identity\Users\Listeners\UserProfile;
use AwardForce\Modules\Identity\Users\Repositories\EloquentGlobalCommunicationChannelRepository;
use AwardForce\Modules\Identity\Users\Repositories\EloquentGlobalUserRepository;
use AwardForce\Modules\Identity\Users\Repositories\EloquentUserRepository;
use AwardForce\Modules\Identity\Users\Services\Emulation\UserEmulator;
use AwardForce\Modules\Notifications\Events\InvalidRecipient as InvalidRecipientEvent;

class UsersServiceProvider extends ServiceProvider
{
    public $defer = true;

    /**
     * Repositories defined by the users module.
     *
     * @var array
     */
    protected $repositories = [
        UserRepository::class => EloquentUserRepository::class,
        GlobalCommunicationChannelRepository::class => EloquentGlobalCommunicationChannelRepository::class,
        GlobalUserRepository::class => EloquentGlobalUserRepository::class,
    ];

    protected $files = [
        __DIR__.'/macros.php',
    ];

    /**
     * Define the listeners for this module.
     *
     * @var array
     */
    protected $listeners = [
        EmailWasChanged::class => UserProfile::class.'@whenEmailWasChanged',
        MobileWasChanged::class => UserProfile::class.'@whenMobileWasChanged',
        UserWasConfirmed::class => UserProfile::class.'@whenUserWasConfirmed',
        InvalidRecipientEvent::class => InvalidRecipientListener::class.'@whenRecipientWasInvalid',
        GlobalUserPendingUpdate::class => GlobalUser::class.'@whenPendingUpdate',
        UserWasDestroyed::class => UserDestroyedListener::class,
    ];

    protected $aliases = [
        'JediEmulator' => UserEmulator::class,
    ];

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return array_keys($this->repositories);
    }

    /**
     * Get the events that trigger this service provider to register.
     *
     * @return array
     */
    public function when()
    {
        return array_keys($this->listeners);
    }
}
