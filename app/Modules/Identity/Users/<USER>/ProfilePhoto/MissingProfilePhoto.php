<?php

namespace AwardForce\Modules\Identity\Users\Services\ProfilePhoto;

use AwardForce\Modules\Identity\Users\Models\User;

class MissingProfilePhoto implements ProfilePhoto
{
    public function __construct(protected User $user)
    {
    }

    public function toArray(int $width, int $height): array
    {
        return [
            'image' => $this->image($width, $height),
            'fullName' => $this->user->fullName(),
            'initials' => $this->user->initials,
            'color' => $this->user->color,
        ];
    }

    public function image(int $width, int $height): string
    {
        return '';
    }
}
