<?php

namespace AwardForce\Modules\Identity\Roles\Services;

use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use Illuminate\Support\Facades\DB;

/**
 * The permissions migration service is designed specifically for migrating roles during permissions updates. For
 * example, whenever a new resource is required, or if a resource or action combination needs to be removed, this
 * service can be used for those requirements.
 *
 * Usage:
 *
 *   $roles = $service->roles(function($role) { return ChapterManager::appliesTo($role); } );
 *
 *   foreach ($roles as $role) {
 *      $service->addResource($role, 'Tags');
 *   }
 */
class PermissionsMigrationService
{
    private $roles;
    private $permissions;

    public function __construct(RoleRepository $roles, PermissionsService $permissions)
    {
        $this->roles = $roles;
        $this->permissions = $permissions;
    }

    /**
     * Retrieve all roles on the system, optionally filtered by a callback.
     *
     * @param  callable  $callback
     * @return mixed
     */
    public function roles(?\Closure $callback = null)
    {
        $roles = $this->roles->relaxed(fn() => $this->roles->getAll());

        if ($callback) {
            $roles = $roles->filter($callback);
        }

        return $roles;
    }

    public function addResource(Role $role, $resource)
    {
        $this->permissions->sync($role, [$resource => ['create' => 'allow', 'view' => 'allow', 'update' => 'allow', 'delete' => 'allow']]);
    }

    /**
     * Forget the specified permission resource from the database.
     */
    public function forgetResource(string $resource)
    {
        DB::table('permissions')->whereResource($resource)->delete();
    }
}
