<?php

namespace AwardForce\Modules\Contract\Events;

use AwardForce\Modules\Audit\Events\Activity;
use AwardForce\Modules\Audit\Events\Log;
use AwardForce\Modules\Audit\Events\SystemResource;
use AwardForce\Modules\Entries\Models\Contract;

class ContractWasDeleted implements Activity
{
    public $contract;

    public function __construct(Contract $contract)
    {
        $this->contract = $contract;
    }

    /**
     * Returns a log of the activity that was undertaken.
     */
    public function log(): Log
    {
        return Log::withDefaults(
            new SystemResource('contact'),
            'deleted',
            'audit.contract.deleted',
            $this->contract,
            $this->contract->id,
            (string) $this->contract->slug,
        );
    }
}
