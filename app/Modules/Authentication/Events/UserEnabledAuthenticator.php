<?php

namespace AwardForce\Modules\Authentication\Events;

use AwardForce\Modules\Audit\Events\Activity;
use AwardForce\Modules\Audit\Events\Log;
use AwardForce\Modules\Audit\Events\SystemResource;
use AwardForce\Modules\Identity\Users\Models\User;

class UserEnabledAuthenticator implements Activity
{
    /**
     * @var User
     */
    public $user;

    /**
     * @var string
     */
    public $authenticator;

    public function __construct(User $user, string $authenticator)
    {
        $this->user = $user;
        $this->authenticator = $authenticator;
    }

    /**
     * Returns a log of the activity that was undertaken.
     */
    public function log(): Log
    {
        return Log::withDefaults(new SystemResource('authenticator'), 'enabled', 'audit.authenticator.enabled', ['title' => $this->authenticator], slug: (string) $this->user->slug);
    }
}
