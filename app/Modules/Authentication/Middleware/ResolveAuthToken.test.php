<?php

namespace AwardForce\Modules\Authentication\Middleware;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Authentication\Tokens\AuthToken;
use AwardForce\Modules\Identity\Users\Contracts\GlobalUserRepository;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\GlobalUser;
use AwardForce\Modules\Identity\Users\Models\UserFactory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Mockery as m;
use Platform\Tokens\TokenManager;
use Ramsey\Uuid\Uuid;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ResolveAuthTokenTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private $request;
    private $tokens;
    protected $user;
    private $globalUser;
    private $middleware;
    private $userFactory;
    private $globalUsers;

    public function init()
    {
        $this->user = $this->setupUserWithRole('Entrant');

        $this->globalUser = new GlobalUser;
        $this->globalUser->id = Uuid::uuid4();
        $this->globalUser->memberships = [current_account()->globalId];

        $this->user->setRelation('globalUser', $this->globalUser);

        $this->request = m::mock(Request::class);
        $this->users = m::mock(UserRepository::class);
        $this->tokens = m::mock(TokenManager::class);
        app()->instance(TokenManager::class, $this->tokens);
        $this->userFactory = m::mock(UserFactory::class);
        app()->instance(UserFactory::class, $this->userFactory);
        $this->globalUsers = m::mock(GlobalUserRepository::class);
        app()->instance(GlobalUserRepository::class, $this->globalUsers);
        $this->middleware = new ResolveAuthToken(app(Manager::class), $this->tokens);
    }

    public function testPassThroughEmptyToken(): void
    {
        $this->request->shouldReceive('get')->once()->andReturn(null);

        $result = $this->middleware->handle($this->request, function (Request $request) {
            return $request;
        });

        $this->assertEquals($this->request, $result);
    }

    public function testInvalidToken(): void
    {
        $this->request->shouldReceive('get')->once()->andReturn($token = 'token');
        $this->tokens->shouldReceive('has')->with($token, AuthToken::class)->once()->andReturn(false);

        $result = $this->middleware->handle($this->request, function (Request $request) {
            return $request;
        });

        $this->assertInstanceOf(RedirectResponse::class, $result);
        $this->assertStringEndsWith('.app', $result->getTargetUrl());
    }

    public function testValidToken(): void
    {
        $this->request->shouldReceive('get')->once()->andReturn($token = 'token');
        $this->tokens->shouldReceive('has')->with($token, AuthToken::class)->once()->andReturn(true);
        $this->tokens->shouldReceive('get')->andReturn(new AuthToken((string) $this->user->globalId), []);
        $this->userFactory->shouldReceive('requireUserByGlobalId')->andReturn($this->user);

        $result = $this->middleware->handle($this->request, function (Request $request) {
            return $request;
        });

        $this->assertEquals($this->request, $result);
        $this->assertEquals($this->user->id, \Consumer::user()->id);
    }
}
