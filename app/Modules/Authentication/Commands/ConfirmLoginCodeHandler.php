<?php

namespace AwardForce\Modules\Authentication\Commands;

use AwardForce\Modules\Notifications\Services\Client\SmsClients;
use Platform\Support\Values\PhoneNumber;

class ConfirmLoginCodeHandler
{
    public function __construct(private SmsClients $clients)
    {
    }

    public function handle(ConfirmLoginCode $command)
    {
        // Temp solution until we start fully using Verify Guard Service in Twilio
        if (PhoneNumber::check($command->login() ?? '')) {
            $this->clients->default()->confirmVerify($command->login());
        }
    }
}
