<?php

namespace AwardForce\Modules\Authentication\Tokens;

use Platform\Tokens\StringToken;
use Platform\Tokens\Token;

class AuthToken implements Token
{
    use StringToken;

    /** @var string */
    public $globalUserId;

    public function __construct(string $globalUserId)
    {
        $this->globalUserId = $globalUserId;
    }

    public function expires(): int
    {
        return 5; // expire in 5 minutes
    }

    public function accountId()
    {
        return null;
    }
}
