<?php

namespace AwardForce\Modules\Menu\Settings\Links\Integrations;

use AwardForce\Library\Authorization\Consumer;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class Legacy extends Link
{
    public function name(): string
    {
        return 'api-integrations';
    }

    public function text(): string
    {
        return trans('integrations.titles.main');
    }

    public function link(): string
    {
        return route('integration.index');
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return '';
    }

    public function applies(): bool
    {
        return Consumer::can('view', 'Integrations');
    }

    public function contexts(): array
    {
        return [new Manage];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
