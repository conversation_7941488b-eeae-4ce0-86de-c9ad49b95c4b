<?php

namespace AwardForce\Modules\Menu\Settings\Links\Audit;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Authentication\Services\Emulator\Facades\JediEmulator;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class AuditLog extends Link
{
    public function name(): string
    {
        return 'audit-log';
    }

    public function text(): string
    {
        return trans('audit.titles.main');
    }

    public function link(): string
    {
        if (feature_disabled('audit')) {
            return route('feature.disabled', ['audit']);
        }

        return route('audit.index');
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return '';
    }

    public function applies(): bool
    {
        return Consumer::can('view', 'Audit') || (JediEmulator::active() && Consumer::isOwner());
    }

    public function contexts(): array
    {
        return [new Manage];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
