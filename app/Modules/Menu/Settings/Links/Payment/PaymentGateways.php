<?php

namespace AwardForce\Modules\Menu\Settings\Links\Payment;

use AwardForce\Library\Authorization\Consumer;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class PaymentGateways extends Link
{
    public function name(): string
    {
        return 'gateways';
    }

    public function text(): string
    {
        return trans('payments.tabs.gateways');
    }

    public function link(): string
    {
        return route('payment.gateways');
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return '';
    }

    public function applies(): bool
    {
        if (is_goodgrants() && feature_disabled('order_payments')) {
            return false;
        }

        return Consumer::can('view', 'Settings');
    }

    public function contexts(): array
    {
        return [new Manage];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
