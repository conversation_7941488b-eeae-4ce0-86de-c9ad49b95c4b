<?php

namespace AwardForce\Modules\GrantReports\Search\Filters;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Entries\Search\Filters\CategorySearchFilter as EntryCategorySearchFilter;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use Illuminate\Support\Collection;

class CategorySearchFilter extends EntryCategorySearchFilter
{
    public function toHtml()
    {
        return view('assignment.search.filters.category')->with([
            'divisions' => $this->divisions,
            'formType' => Form::FORM_TYPE_ENTRY,
            'categories' => $this->categories(),
        ])->render();
    }

    protected function categories(): Collection
    {
        return $this->categories->getAllForEntriesInExistingGrantReports(Consumer::user()->id);
    }
}
