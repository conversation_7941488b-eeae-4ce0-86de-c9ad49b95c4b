<?php

namespace AwardForce\Modules\Reports\Search;

use AwardForce\Modules\Reports\ReportRepository;
use AwardForce\Modules\Reports\Search\Columns\Consigner;
use AwardForce\Modules\Reports\Search\Columns\Deleted;
use AwardForce\Modules\Reports\Search\Columns\Key;
use AwardForce\Modules\Reports\Search\Columns\Pdf;
use AwardForce\Modules\Reports\Search\Filters\ConsignerSearchFilter;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Slug;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;

class ReportColumnator extends Columnator
{
    /**
     * Return the resource that this columnator represents (such as entries, or users).
     *
     * @return string|null
     */
    public function resource()
    {
        return null;
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = new Dependencies;

        $dependencies->add((new ColumnFilter(...$this->columns($view)))->with('reports.id', 'reports.slug'));
        $dependencies->add(new IncludeFilter('consigner', 'consigner.currentMembership'));
        $dependencies->add(new ConsignerSearchFilter($this->input));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(OrderFilter::fromColumns($this->columns($view), $this->input, 'reports.created_at')->uniqueColumn('reports.id'));

        return $dependencies;
    }

    /**
     * Base columns are those that are static and essentially not columns based off any custom
     * field configuration. For example, a user's first and last name are base columns.
     *
     * @return mixed
     */
    protected function baseColumns()
    {
        return new Columns([
            Slug::forResource('reports'),
            new Key,
            new Consigner,
            new Deleted(trans('search.columns.deleted'), 'reports.deleted', 'reports.created_at'),
            new Pdf,
        ]);
    }

    /**
     * Each columnator must have a unique key that can be used to identify it when loading from settings.etc.
     */
    public static function key(): string
    {
        return 'report.search';
    }

    /**
     * All columnators have an export view as well. The export key represents a value that can be used to search
     * for any saved exports for this particular columnator.
     */
    public static function exportKey(): string
    {
        return '';
    }

    /**
     * Return the repository to be used for future queries.
     */
    public function repository(): Repository
    {
        return app(ReportRepository::class);
    }
}
