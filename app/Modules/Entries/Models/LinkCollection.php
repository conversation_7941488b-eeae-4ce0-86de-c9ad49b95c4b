<?php

namespace AwardForce\Modules\Entries\Models;

use Illuminate\Support\Facades\Validator;
use Platform\Database\Eloquent\Collection;

class LinkCollection extends Collection
{
    const DELIMITER = ', ';

    /**
     * Transforms a collection of entry links into a readable format for exports.
     *
     * @return string
     */
    public function formatForExport()
    {
        return $this->map(function (Link $link) {
            $extra = $link->extra ? ' ('.$link->extra.')' : '';

            return $link->url.$extra;
        })->implode(self::DELIMITER);
    }

    public function filterOutInvalidLinks(): self
    {
        $this->items = collect($this->items)
            ->reject(fn(Link $link) => Validator::make(['url' => $link->url], ['url' => 'url'])->fails())
            ->values()
            ->all();

        return $this;
    }
}
