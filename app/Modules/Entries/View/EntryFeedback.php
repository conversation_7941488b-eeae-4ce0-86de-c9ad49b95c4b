<?php

namespace AwardForce\Modules\Entries\View;

use Illuminate\Support\Collection;

class EntryFeedback
{
    /**
     * @var Collection
     */
    private $blocks;

    /**
     * @var Collection
     */
    private $extra;

    /**
     * @var Collection
     */
    private $scores;

    /**
     * EntryFeedback constructor.
     */
    public function __construct(
        Collection $blocks,
        Collection $extra,
        Collection $scores
    ) {
        $this->blocks = $blocks;
        $this->extra = $extra;
        $this->scores = $scores;
    }

    public function scores($type, $id = null)
    {
        if ($id) {
            return $this->scores->get($type)->get($id);
        }

        return $this->scores->get($type, []);
    }

    public function blocks()
    {
        return $this->blocks;
    }

    public function extraCriteria()
    {
        return $this->extra;
    }
}
