<?php

namespace AwardForce\Modules\Entries\Search\Columns\Duplicates;

use AwardForce\Library\Search\Columns\TranslatedColumnWithFallback;
use AwardForce\Modules\Entries\Search\Filters\CategorySearchFilter;
use Illuminate\Support\Collection;
use Platform\Search\Defaults;

class Category extends TranslatedColumnWithFallback
{
    public function fieldName(): string
    {
        return 'name';
    }

    public function title()
    {
        return trans('entries.table.columns.category');
    }

    public function name(): string
    {
        return 'manage_duplicates.category';
    }

    public function dependencies(): Collection
    {
        return collect([
            CategorySearchFilter::class,
        ]);
    }

    public function html($duplicate)
    {
        return $this->value($duplicate);
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function priority(): int
    {
        return 60;
    }

    public function sortable(): bool
    {
        return true;
    }

    public function key()
    {
        return "{$this->alias()}_{$this->fieldName()}";
    }
}
