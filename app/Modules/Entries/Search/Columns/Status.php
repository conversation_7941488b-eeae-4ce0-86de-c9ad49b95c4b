<?php

namespace AwardForce\Modules\Entries\Search\Columns;

use AwardForce\Modules\Entries\Models\Entry;
use Illuminate\Support\HtmlString;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Defaults;

class Status extends ReviewStatus implements ApiColumn
{
    /**
     * Title for the column - used for headers.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function title()
    {
        return trans('entries.table.columns.status');
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'manage_entries.status';
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @param  Entry  $entry
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function html($entry)
    {
        $orders = PaymentStatus::orders($entry)->all();

        return new HtmlString(view('entry.manager.search.entry-status', ['entry' => $entry, 'orders' => $orders, 'moderationStatus' => true, 'reviewStatus' => $this->value($entry)]));
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('search');
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return false;
    }

    public function apiName()
    {
        return 'status';
    }

    public function apiValue($record)
    {
        return $record->submissionStatus();
    }

    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('all');
    }
}
