<?php

namespace AwardForce\Modules\Entries\Search\Columns\Files;

use AwardForce\Modules\Api\V2\Services\ApiFieldTransformer;
use AwardForce\Modules\Files\Models\File;
use Illuminate\Support\Collection;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Transcoding implements ApiColumn, Column
{
    use ApiFieldTransformer;

    public function default(): Defaults
    {
        return new Defaults('none');
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field()
    {
    }

    public function fixed(): bool
    {
        return false;
    }

    public function html($record)
    {
    }

    public function name(): string
    {
        return 'files.transcoding';
    }

    public function priority(): int
    {
        return 100;
    }

    public function sortable(): bool
    {
        return false;
    }

    public function title()
    {
    }

    public function visible(): bool
    {
        return false;
    }

    public function value($record)
    {
        return $record->value;
    }

    public function apiName()
    {
        return 'transcoding';
    }

    /**
     * @param  File  $record
     * @return array|string
     */
    public function apiValue($record)
    {
        if ($record->isVideo()) {
            return $this->transcodingPieces($record->file);
        }

        return [];
    }

    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('view');
    }
}
