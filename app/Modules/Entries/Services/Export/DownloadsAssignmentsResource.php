<?php

namespace AwardForce\Modules\Entries\Services\Export;

trait DownloadsAssignmentsResource
{
    public array $assignments;

    protected function bulkDownloadResource(string $directory)
    {
        $assignments = ! empty($this->assignments) ? $this->assignmentRepository->getByIds($this->assignments) : [];

        $assignments->groupBy('entry_id')->each(function ($assignments) use ($directory) {
            $entry = $assignments->pluck('entry')->first();
            $scoreSets = $this->scoreSetRepository->getByIds($assignments->pluck('score_set_id')->all());
            $this->gatherSubmittableContent($entry, $directory, true, $scoreSets);
        });
    }
}
