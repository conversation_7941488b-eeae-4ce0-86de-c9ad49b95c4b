<?php

namespace AwardForce\Modules\ScheduledTasks\Services\ActionExecutors;

use Carbon\CarbonImmutable;

abstract class AbstractActionExecutor implements ActionExecutor
{
    protected function __construct(protected CarbonImmutable $sendTime, protected array $payload = [])
    {
    }

    public function sendTime(): ?CarbonImmutable
    {
        return $this->sendTime;
    }

    public function payload(): string
    {
        return json_encode($this->payload);
    }

    protected function shouldBeExecuted(): bool
    {
        return $this->sendTime && $this->sendTime->isPast();
    }

    abstract public static function create(CarbonImmutable $sendTime);

    abstract public function calculateNewDueDate(CarbonImmutable $dueDate): CarbonImmutable;

    public static function createFromPayload(string $payload, CarbonImmutable $dueDate): AbstractActionExecutor
    {
        $class = static::class;
        $instance = new $class($dueDate);
        $instance->payload = json_decode($payload, true);
        $instance->validatePayload();

        return $instance;
    }
}
