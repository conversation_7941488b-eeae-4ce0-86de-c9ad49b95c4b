<?php

namespace AwardForce\Modules\AllocationPayments\Events;

use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use AwardForce\Modules\Audit\Events\Log;
use AwardForce\Modules\Audit\Events\SystemResource;

class AllocationPaymentWasDeleted extends AllocationPaymentBaseEvent
{
    public function __construct(AllocationPayment $allocationPayment)
    {
        parent::__construct($allocationPayment);
    }

    /**
     * Returns a log of the activity that was undertaken.
     */
    public function log(): Log
    {
        return Log::withDefaults(
            new SystemResource($resource = 'allocation_payment'),
            $action = 'deleted',
            "audit.$resource.$action",
            $this->allocationPayment,
            (string) $this->allocationPayment->id,
            (string) $this->allocationPayment->slug
        );
    }
}
