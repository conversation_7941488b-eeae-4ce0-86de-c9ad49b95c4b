<?php

namespace AwardForce\Modules\Accounts\Services\Copy;

use AwardForce\Modules\Accounts\Commands\CopyConfigurationFiles;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\AwardsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\CategoriesCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\ChaptersCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\ContentBlocksCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\Copier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\CurrenciesCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\DefaultLocaleCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\DiscountsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\DocumentTemplatesCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\EntryFieldsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\ExportLayoutsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\FieldsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\FormsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\FundsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\GrantStatusesCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\InterfaceTextCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\LanguageCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\NotificationsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\PanelsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\PaymentMethodsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\PaymentScheduleTemplatesCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\PaymentsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\PricesCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\PricingRulesCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\ReviewStagesCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\RolesCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\RoundsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\ScoreSetsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\ScoringCriteriaCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\SeasonalUserFieldsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\SeasonCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\SocialCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\TabsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\TagsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\TaxesCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\TermsCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\ThemeCopier;
use AwardForce\Modules\Accounts\Services\Copy\Copiers\UserFieldsCopier;
use Illuminate\Foundation\Bus\DispatchesJobs;

class CopierService
{
    use DispatchesJobs;

    const STRATEGY_RETAIN = 'retain';

    const STRATEGY_REPLACE = 'replace';

    public static array $map = [
        'awards' => AwardsCopier::class,
        'categories' => CategoriesCopier::class,
        'chapters' => ChaptersCopier::class,
        'content_blocks' => ContentBlocksCopier::class,
        'currencies' => CurrenciesCopier::class,
        'default_locale' => DefaultLocaleCopier::class,
        'discounts' => DiscountsCopier::class,
        'export_layouts' => ExportLayoutsCopier::class,
        'entry_fields' => EntryFieldsCopier::class,
        'fields' => FieldsCopier::class,
        'forms' => FormsCopier::class,
        'funds' => FundsCopier::class,
        'grant_statuses' => GrantStatusesCopier::class,
        'interface_texts' => InterfaceTextCopier::class,
        'languages' => LanguageCopier::class,
        'notifications' => NotificationsCopier::class,
        'order_payments' => PaymentsCopier::class,
        'panels' => PanelsCopier::class,
        'payment_methods' => PaymentMethodsCopier::class,
        'payment_schedule_templates' => PaymentScheduleTemplatesCopier::class,
        'prices' => PricesCopier::class,
        'pricing_rules' => PricingRulesCopier::class,
        'review_stages' => ReviewStagesCopier::class,
        'roles' => RolesCopier::class,
        'rounds' => RoundsCopier::class,
        'score_sets' => ScoreSetsCopier::class,
        'scoring_criteria' => ScoringCriteriaCopier::class,
        'season' => SeasonCopier::class,
        'seasonal_user_fields' => SeasonalUserFieldsCopier::class,
        'social' => SocialCopier::class,
        'tabs' => TabsCopier::class,
        'tags' => TagsCopier::class,
        'taxes' => TaxesCopier::class,
        'terms' => TermsCopier::class,
        'theme' => ThemeCopier::class,
        'user_fields' => UserFieldsCopier::class,
        'document_templates' => DocumentTemplatesCopier::class,
    ];
    private static string $settingsStrategy;
    private static string $seasonStrategy;
    private static string $settingsCopyTerm;
    private static string $seasonCopyTerm;
    private static ?int $targetSeasonId = null;
    private static string $sourceRegion;
    private static array $fileCopies = [];
    private bool $copyInProgress = false;
    private static bool $hasChapters = false;

    public static function settingsStrategy(): string
    {
        return self::$settingsStrategy;
    }

    public static function seasonStrategy(): string
    {
        return self::$seasonStrategy;
    }

    public static function settingsCopyTerm(): string
    {
        return self::$settingsCopyTerm;
    }

    public static function seasonCopyTerm(): string
    {
        return self::$seasonCopyTerm;
    }

    public static function targetSeasonId(): ?int
    {
        return self::$targetSeasonId;
    }

    public static function getSourceRegion(): string
    {
        return self::$sourceRegion;
    }

    public static function setSourceRegion(string $sourceRegion): void
    {
        self::$sourceRegion = $sourceRegion;
    }

    public function setSettingsStrategy(string $strategy): void
    {
        self::$settingsStrategy = $strategy;
    }

    public function setSeasonStrategy(string $strategy): void
    {
        self::$seasonStrategy = $strategy;
    }

    public function setSettingsCopyTerm(string $copyTerm): void
    {
        self::$settingsCopyTerm = $copyTerm;
    }

    public function setSeasonCopyTerm(string $copyTerm): void
    {
        self::$seasonCopyTerm = $copyTerm;
    }

    public function setTargetSeasonId(?int $seasonId): void
    {
        self::$targetSeasonId = $seasonId;
    }

    public static function queueFileCopy(CopyConfigurationFiles $command): void
    {
        self::$fileCopies[] = $command;
    }

    public function copyFiles(): void
    {
        foreach (self::$fileCopies as $command) {
            $this->dispatch($command);
        }
    }

    public function hasQueuedFiles(): bool
    {
        return ! empty(self::$fileCopies);
    }

    /*
     * Returns an array of Copier instances in the order they should be initialized.
     * The order is determined by the dependencies of each Copier.
     */
    public function getOrderedCopiers(): array
    {
        $services = [];
        foreach (array_keys(self::$map) as $key) {
            $service = $this->getCopier($key);
            if ($service) {
                $services[] = $service;
            }
        }

        return $this->getInitializationOrder($services);
    }

    public function getCopier(string $resource): ?Copier
    {
        if (! isset(self::$map[$resource])) {
            return null;
        }

        return app(self::$map[$resource]);
    }

    public function getInitializationOrder($services): array
    {
        $graph = $this->buildDependencyGraph($services);
        $visited = [];
        $order = [];

        foreach (array_keys($graph) as $node) {
            if (! in_array($node, $visited)) {
                $this->visitNode($node, $graph, $visited, $order);
            }
        }

        return $order;
    }

    private function buildDependencyGraph($services): array
    {
        $graph = [];

        foreach ($services as $service) {
            if (is_string($service)) {
                $serviceName = $service;
                $service = app($serviceName);
            } else {
                $serviceName = get_class($service);
            }
            $dependencies = $service->dependencies();
            $graph[$serviceName] = $dependencies;
        }

        return $graph;
    }

    private function visitNode($node, &$graph, &$visited, &$order): void
    {
        $visited[] = $node;

        if (isset($graph[$node])) {
            foreach ($graph[$node] as $dependency) {
                if (! in_array($dependency, $visited)) {
                    $this->visitNode($dependency, $graph, $visited, $order);
                }
            }
        }

        $order[] = app($node)->key();
    }

    public function toggleCopyInProgress(bool $copyInProgress = true): void
    {
        $this->copyInProgress = $copyInProgress;
    }

    public function copyInProgress(): bool
    {
        return $this->copyInProgress;
    }

    public function clear(): void
    {
        self::$fileCopies = [];
        self::$seasonCopyTerm = '';
        self::$settingsCopyTerm = '';
        self::$targetSeasonId = null;
        self::$settingsStrategy = '';
        self::$seasonStrategy = '';
        self::$sourceRegion = '';
        self::$hasChapters = false;
        $this->copyInProgress = false;
    }

    public static function newSeason(): bool
    {
        return ! self::$targetSeasonId;
    }

    public function setHasChapters(bool $hasChapters): void
    {
        self::$hasChapters = $hasChapters;
    }

    public static function hasChapters(): bool
    {
        return self::$hasChapters;
    }
}
