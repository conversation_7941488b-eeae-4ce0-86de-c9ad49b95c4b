<?php

namespace AwardForce\Modules\Accounts\Services\Copy\Values;

use Assert\Assertion;
use Illuminate\Support\Fluent;

class CopySettings extends Fluent
{
    use CastsValueObjects;

    public function __construct($attributes = [])
    {
        $this->validate($attributes);
        parent::__construct($this->castAttributes($attributes));
    }

    private function validate(array $attributes)
    {
        Assertion::keyExists($attributes, 'configuration');
        Assertion::keyExists($attributes, 'season');
        Assertion::keyExists($attributes, 'resources');
        Assertion::keyExists($attributes, 'forms');
    }

    public function __clone(): void
    {
        $this->attributes = $this->castAttributes(json_decode(json_encode($this->attributes), true));
    }
}
