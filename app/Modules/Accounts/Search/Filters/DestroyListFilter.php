<?php

namespace AwardForce\Modules\Accounts\Search\Filters;

use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class DestroyListFilter implements ColumnatorFilter, SearchFilter
{
    public function applies(): bool
    {
        return true;
    }

    public function applyToEloquent($query)
    {
        return $query->whereSuspended(true)
            ->whereNotNull('accounts.destruction_due_date');
    }
}
