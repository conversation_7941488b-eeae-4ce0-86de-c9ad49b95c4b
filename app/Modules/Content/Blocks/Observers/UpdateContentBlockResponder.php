<?php

namespace AwardForce\Modules\Content\Blocks\Observers;

use AwardForce\Modules\Content\Blocks\Contracts\UpdateContentBlockResponderInterface;

class UpdateContentBlockResponder implements UpdateContentBlockResponderInterface
{
    public function onSuccess()
    {
        return redirect()->route('content-block.index');
    }

    public function onFailure()
    {
        return redirect()->back()->withInput();
    }

    public function onValidationError($errors)
    {
        return redirect()->back()->withInput()->withErrors($errors);
    }
}
