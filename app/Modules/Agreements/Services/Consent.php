<?php

namespace AwardForce\Modules\Agreements\Services;

use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Settings\Contracts\SettingRepository;

class Consent
{
    /** @var SettingRepository */
    private $settings;

    /**
     * @return SettingRepository $settings
     */
    public function __construct(SettingRepository $settings)
    {
        $this->settings = $settings;
    }

    public function missing(User $user): bool
    {
        return $this->agreementToTermsMissing($user) || $this->consentToNotificationsMissing($user);
    }

    public function collected(User $user): bool
    {
        return ! $this->missing($user);
    }

    public function agreementToTermsMissing(User $user): bool
    {
        return $this->settings->getValueByKey('require-agreement-to-terms')
            && ! $user->agreementToTermsCollected();
    }

    public function consentToNotificationsMissing(User $user): bool
    {
        return $this->consentToNotificationMissingForGuestUser() && ! $user->consentToNotificationsCollected();
    }

    public function consentToNotificationMissingForGuestUser(): bool
    {
        return (bool) $this->settings->getValueByKey('require-consent-to-notifications-and-broadcasts');
    }
}
