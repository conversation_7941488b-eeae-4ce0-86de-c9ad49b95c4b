<?php

namespace AwardForce\Modules\Agreements\Models;

use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Modules\Accounts\Traits\BelongsToAccount;
use AwardForce\Modules\Agreements\Events\AgreementWasReset;
use AwardForce\Modules\Agreements\Events\AgreementWasSigned;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Seasons\Traits\Seasonal;
use Carbon\Carbon;
use Eloquence\Behaviours\HasSlugs;

/**
 * AwardForce\Modules\Agreements\Models\Agreement
 *
 * @property int $id
 * @property int $accountId
 * @property int|null $seasonId
 * @property int $userId
 * @property int|null $scoreSetId
 * @property string|null $slug
 * @property string $type
 * @property string|null $content
 * @property string|null $signature
 * @property \Illuminate\Support\Carbon|null $signedAt
 * @property \Illuminate\Support\Carbon|null $resetAt
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property string|null $deletedAt
 * @property-read \AwardForce\Modules\Accounts\Models\Account $account
 * @property-read ScoreSet|null $scoreSet
 * @property-read \AwardForce\Modules\Seasons\Models\Season|null $season
 * @property-read User $user
 *
 * @method static \Platform\Database\Eloquent\Collection<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Agreement forSeason($seasonId)
 * @method static \Platform\Database\Eloquent\Collection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Agreement newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|Agreement newQuery()
 * @method static \Platform\Database\Eloquent\Builder|Agreement preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|Agreement query()
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereContent($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereDeletedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereResetAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereScoreSetId($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereSeasonId($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereSignature($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereSignedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereSlug($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereType($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereUpdatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Agreement whereUserId($value)
 *
 * @mixin \Eloquent
 */
class Agreement extends Model
{
    use BelongsToAccount;
    use HasSlugs;
    use Seasonal;

    const TYPE_JUDGING = 'judging';

    const TYPE_AGREEMENT_TO_TERMS = 'agreement-to-terms';

    const TYPE_CONSENT_TO_NOTIFICATIONS_AND_BROADCASTS = 'consent-to-notifications-and-broadcasts';

    const TYPE_COOKIES = 'cookies';

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'agreements';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'disabled_at' => 'datetime',
        'signed_at' => 'datetime',
        'reset_at' => 'datetime',
    ];

    /**
     * Creates an instance of a signed agreement.
     *
     * @param  string  $content
     * @param  string  $signature
     * @return Agreement
     */
    public static function sign($userId, $type, $content = null, $signature = null, $seasonId = null, $scoreSetId = null)
    {
        $agreement = new self;

        $agreement->userId = $userId;
        $agreement->type = $type;
        $agreement->content = $content;
        $agreement->signature = $signature;
        $agreement->seasonId = $seasonId;
        $agreement->scoreSetId = $scoreSetId;
        $agreement->signedAt = Carbon::now();

        $agreement->raise(new AgreementWasSigned($agreement));

        return $agreement;
    }

    /**
     * Each agreement belongs to a specific user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function scoreSet()
    {
        return $this->belongsTo(ScoreSet::class);
    }

    /**
     * Resets the agreement.
     */
    public function reset()
    {
        $this->attributes['reset_at'] = Carbon::now();

        $this->raise(new AgreementWasReset($this));
    }

    /**
     * Protect against field setting directly.
     *
     * @throws \Exception
     */
    public function setResetAtAttribute()
    {
        throw new \Exception('Please set the reset_at attribute via $agreement->reset() method.');
    }

    /**
     * @return bool
     */
    public function judging()
    {
        return $this->type === self::TYPE_JUDGING;
    }

    /**
     * @return bool
     */
    public function agreementToTerms()
    {
        return $this->type === self::TYPE_AGREEMENT_TO_TERMS;
    }

    /**
     * @return bool
     */
    public function consentToNotificationsAndBroadcasts()
    {
        return $this->type === self::TYPE_CONSENT_TO_NOTIFICATIONS_AND_BROADCASTS;
    }

    /**
     * @return bool
     */
    public function agreementToCookies()
    {
        return $this->type === self::TYPE_COOKIES;
    }
}
