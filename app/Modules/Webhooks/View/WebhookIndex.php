<?php

namespace AwardForce\Modules\Webhooks\View;

use AwardForce\Library\Search\ColumnatorCacheSearch;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Search\Services\SavedViews;
use AwardForce\Modules\Webhooks\Contracts\WebhookRepository;
use AwardForce\Modules\Webhooks\Search\WebhookColumnator;
use Illuminate\Http\Request;
use Platform\View\View;

class WebhookIndex extends View
{
    use SavedViews;

    /** @var ColumnatorFactory */
    private $columnators;

    /** @var WebhookRepository */
    private $webhookRepository;

    /** @var Request */
    private $request;

    /** @var WebhookColumnator */
    public $webhookColumnator;

    public function __construct(
        ColumnatorFactory $columnators,
        WebhookRepository $webhookRepository,
        Request $request
    ) {
        $this->columnators = $columnators;
        $this->webhookRepository = $webhookRepository;
        $this->request = $request;
    }

    public function webhooks()
    {
        $search = new ColumnatorCacheSearch($this->columnator);

        return $search->search();
    }

    /**
     * @return \Platform\Search\Columnator
     */
    public function columnator()
    {
        return $this->webhookColumnator ?? $this->webhookColumnator = $this->columnators->forArea($this->area(), $this->request->all());
    }

    public function webhookIds(): array
    {
        return $this->webhooks->pluck('id')->toArray();
    }

    /**
     * @return string
     */
    public function area()
    {
        return 'webhook.search';
    }
}
