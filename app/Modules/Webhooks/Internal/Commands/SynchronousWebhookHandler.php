<?php

namespace AwardForce\Modules\Webhooks\Internal\Commands;

use AwardForce\Modules\Webhooks\BackoffStrategies\ExponentialBackoffStrategy;

class SynchronousWebhookHandler
{
    /** @var ExponentialBackoffStrategy */
    private $backoffStrategy;

    /*  @var int */
    private $attempts = 0;

    public function __construct()
    {
        $this->backoffStrategy = new ExponentialBackoffStrategy(2, 4, 30);
    }

    public function handle(SynchronousWebhook $command)
    {
        $exception = null;
        while ($this->attempts < 5) {
            try {
                $exception = null;
                $command->webhook->ping();
                break;
            } catch (\Exception $e) {
                $exception = $e;
                sleep($this->backoffStrategy->waitInSecondsAfterAttempt($this->attempts++));
            }
        }

        throw_if(! is_null($exception), $exception);
    }
}
