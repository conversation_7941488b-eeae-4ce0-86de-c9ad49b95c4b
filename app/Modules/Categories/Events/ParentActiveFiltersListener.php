<?php

namespace AwardForce\Modules\Categories\Events;

use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use Illuminate\Support\Collection;
use Tectonic\LaravelLocalisation\Translator\Engine;

class ParentActiveFiltersListener
{
    /**
     * @var CategoryRepository
     */
    private $categories;

    /**
     * @var Engine
     */
    private $translator;

    public function __construct(CategoryRepository $categories, Engine $translator)
    {
        $this->categories = $categories;
        $this->translator = $translator;
    }

    public function handle(Collection $filters)
    {
        $filter = $filters->has('parent') ? $filters->get('parent') : null;

        if (! $filter || ! ($parent = $this->categories->getBySlug($filter->value))) {
            return;
        }

        $this->translator->translate($parent);

        $filter->value = lang($parent, 'name');
        $filter->text = trans('category.table.columns.parent');
    }
}
