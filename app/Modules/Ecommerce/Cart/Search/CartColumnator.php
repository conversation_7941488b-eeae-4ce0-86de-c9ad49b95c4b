<?php

namespace AwardForce\Modules\Ecommerce\Cart\Search;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Search\Columns\ActionOverflow;
use AwardForce\Library\Search\Columns\SlugReactiveMarker;
use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Modules\Ecommerce\Cart\Database\Repositories\CartRepository;
use AwardForce\Modules\Ecommerce\Cart\Search\Actions\EmptyCart;
use AwardForce\Modules\Ecommerce\Cart\Search\Actions\ViewCart;
use AwardForce\Modules\Ecommerce\Cart\Search\Columns\Age;
use AwardForce\Modules\Ecommerce\Cart\Search\Columns\Items;
use AwardForce\Modules\Ecommerce\Cart\Search\Columns\Status;
use AwardForce\Modules\Ecommerce\Cart\Search\Columns\UserName;
use AwardForce\Modules\Ecommerce\Cart\Search\Filters\CartStatusFilter;
use AwardForce\Modules\Ecommerce\Cart\Search\Filters\CartsWithItems;
use AwardForce\Modules\Ecommerce\Cart\Search\Filters\CartUserFilter;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\KeywordFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;

class CartColumnator extends Columnator
{
    protected function baseColumns()
    {
        return new Columns([
            new SlugReactiveMarker,
            $this->actionOverflow(),
            new UserName,
            new Items($this->resource()),
            new Status,
            new Age,
        ]);
    }

    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = new Dependencies;

        //Filters
        $dependencies->add((new ColumnFilter(...$this->columns($view)))->with(
            'carts.id',
            'carts.slug',
            'carts.user_id',
            'carts.currency',
        ));
        $dependencies->add(new IncludeFilter([
            'user',
            'items.price',
        ]));
        $dependencies->add(new CartsWithItems);
        $dependencies->add(new CartUserFilter);
        $dependencies->add(new CartStatusFilter($this->input));
        $dependencies->add(new SeasonalFilter(current_account()->activeSeason()->id, 'carts.season_id'));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new GroupingFilter('carts.id'));
        $dependencies->add(OrderFilter::fromColumns($this->columns($view), $this->input, 'carts.age')->uniqueColumn('carts.id'));
        $dependencies->add(new KeywordFilter($this->input['keywords'] ?? '', ['users.first_name', 'users.last_name', 'users.email']));

        return $dependencies;
    }

    public function resource()
    {
        return 'Orders';
    }

    public static function key(): string
    {
        return 'carts.search';
    }

    public static function exportKey(): string
    {
        return 'carts.export';
    }

    public function repository(): Repository
    {
        return app(CartRepository::class);
    }

    private function actionOverflow(): ActionOverflow
    {
        $overflowMenu = (new ActionOverflow($this->key()));

        if (Consumer::can('update', $this->resource())) {
            $overflowMenu
                ->addAction(new ViewCart('cart', $this->resource()))
                ->addAction(new EmptyCart('cart', $this->resource()));
        }

        return $overflowMenu;
    }
}
