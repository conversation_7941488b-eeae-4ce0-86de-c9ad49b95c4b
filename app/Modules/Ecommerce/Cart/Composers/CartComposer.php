<?php

namespace AwardForce\Modules\Ecommerce\Cart\Composers;

use AwardForce\Modules\Accounts\Contracts\SupportedCurrencyRepository;
use AwardForce\Modules\Payments\Enums\CardNetwork;
use AwardForce\Modules\Payments\Repositories\DiscountRepository;
use AwardForce\Modules\Payments\Repositories\PriceRepository;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;
use Tectonic\LaravelLocalisation\Facades\Translator;

class CartComposer
{
    public function __construct(
        private SupportedCurrencyRepository $supportedCurrencies,
        private SettingRepository $settingRepository,
        private PriceRepository $priceRepository,
        private DiscountRepository $discounts
    ) {
    }

    public function compose(View $view)
    {
        $defaultCurrency = $this->supportedCurrencies->getDefault();

        $view->currencies = $this->supportedCurrencies->getAll()->pluck('code')->toArray();
        $view->defaultCurrency = $defaultCurrency ? $defaultCurrency->code : null;

        $settings = $this->settingRepository->getAllAsKeyValue();

        $this->applyPaymentMethods($view, $settings);
        $this->applyPrices($view);

        // Set the processing fee rate (%)
        $this->setProcessingFeeRate($view);

        // Filter and sort the items as we need them
        $view->items = $view->cart->itemCollection()->sortItems();

        // Check if at least one discount code has been configured
        $view->discountsConfigured = $this->discounts->countInSeason($this->seasonId()) > 0;
    }

    /**
     * Apply available payment method and options
     */
    protected function applyPaymentMethods(View $view, $settings)
    {
        $currencyCode = $view->cart->currency()->code;
        $paymentMethods = [];

        if (Arr::get($settings, 'accept-credit-cards')) {
            foreach (Config::get('payments.available_cards') as $value) {
                if (Arr::get($settings, 'accept-'.$value) == 1
                    && $value !== CardNetwork::CartesBancaires->value
                ) {
                    $card = $value;
                    $paymentMethods[trans("payments.form.accept_{$card}.label")] = $card;
                }
            }
        }

        if (Arr::get($settings, 'accept-ideal') &&
            Arr::get($settings, 'payment-gateway') == 'stripe_connect' &&
            $currencyCode == 'EUR') {
            $paymentMethods[trans('payments.form.accept_ideal.label')] = 'ideal';
        }

        if (Arr::get($settings, 'accept-alipay') &&
            Arr::get($settings, 'payment-gateway') == 'stripe_connect' &&
            in_array($currencyCode, ['CNY'])) {
            $paymentMethods[trans('payments.form.accept_alipay.label')] = 'alipay';
        }

        if (Arr::get($settings, 'accept-invoice')) {
            $paymentMethods[trans('payments.form.accept_invoice.label')] = 'invoice';
        }

        if (Arr::get($settings, 'accept-paypal')) {
            $paymentMethods[trans('payments.form.accept_paypal.label')] = 'paypal_express';
        }

        $view->paymentMethods = $paymentMethods;
    }

    protected function applyPrices(View $view)
    {
        $season = $this->seasonId();

        $view->entryFees = Translator::translate(
            $this->priceRepository->getSelectableByTypeAndSeason('entry', $season)
        );

        $view->entrantFees = Translator::translate(
            $this->priceRepository->getSelectableByTypeAndSeason('entrant', $season)
        );
    }

    protected function setProcessingFeeRate(View $view)
    {
        $view->cart->setProcessingFeeRate();
    }

    private function seasonId(): int
    {
        return current_account()->activeSeason()->id;
    }
}
