<?php

namespace AwardForce\Modules\Ecommerce\Cart\Costing\Pricing\EntryPriceVariants;

use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Models\Entry;

trait ChecksCategories
{
    /**
     * A variant for a parent category also applies to every child category.
     *
     * @return bool
     */
    protected function categoryApplies($categoryId, Entry $entry)
    {
        return in_array($categoryId, $this->categories($entry->category));
    }

    /**
     * Returns an array with the ids of the given category and all of it's parents.
     */
    protected function categories(Category $category): array
    {
        $categories = [];

        while ($category) {
            $categories[] = $category->id;
            $category = $category->parent;
        }

        return $categories;
    }
}
