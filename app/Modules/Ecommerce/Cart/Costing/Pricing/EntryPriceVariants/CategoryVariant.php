<?php

namespace AwardForce\Modules\Ecommerce\Cart\Costing\Pricing\EntryPriceVariants;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Payments\Models\PriceAmount;

class CategoryVariant implements Variant
{
    use ChecksCategories;

    /**
     * Return true if the variant can return an amount for the conditions supplied.
     *
     * @return bool
     */
    public function appliesTo(PriceAmount $amount, Entry $entry)
    {
        return empty($amount->chapterId) && $this->categoryApplies($amount->categoryId, $entry) && ! $amount->hasFieldConfiguration();
    }
}
