<?php

namespace AwardForce\Modules\PaymentMethods\Commands;

use AwardForce\Modules\PaymentMethods\Models\PaymentMethod;
use AwardForce\Modules\PaymentMethods\Repositories\PaymentMethodsRepository;
use Platform\Events\EventDispatcher;
use Tectonic\LaravelLocalisation\Database\TranslationService;

class AddPaymentMethodCommandHandler
{
    use EventDispatcher;

    public function __construct(
        protected PaymentMethodsRepository $paymentMethods,
        protected TranslationService $translations,
    ) {
    }

    public function handle(AddPaymentMethodCommand $command)
    {
        $paymentMethod = new PaymentMethod();
        $paymentMethod->value = $command->value;
        $paymentMethod->order = $command->order;
        $this->paymentMethods->save($paymentMethod);
        $this->translations->sync($paymentMethod, $command->translated);
        $this->dispatch($paymentMethod->releaseEvents());

        return $paymentMethod;
    }
}
