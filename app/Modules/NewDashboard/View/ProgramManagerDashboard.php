<?php

namespace AwardForce\Modules\NewDashboard\View;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\NewDashboard\Enums\DashboardProvider;
use <PERSON>\Uuid\Uuid;
use <PERSON>\Uuid\UuidInterface;

class ProgramManagerDashboard extends ViewDashboard
{
    use ForProgramManager;

    public function activeDashboardLink(): string
    {
        return 'program-manager';
    }

    protected function provider(): DashboardProvider
    {
        return DashboardProvider::from(config('dashboard.provider'));
    }

    public function dashboardId(): UuidInterface
    {
        return Uuid::fromString(config('services.embeddable.dashboards.'.Vertical::current().'.programManager'));
    }
}
