<?php

namespace AwardForce\Modules\Payments;

use Assert\Assertion;

class DiscountUse
{
    /**
     * @var string
     */
    private $use;

    public function __construct(string $use)
    {
        Assertion::inArray($use, ['unlimited', 'once-per-entrant']);

        $this->use = $use;
    }

    /**
     * Returns a new discount use object with default.
     *
     * @return DiscountUse
     */
    public static function default()
    {
        return new DiscountUse('unlimited');
    }

    /**
     * Equivalence check.
     *
     * @return bool
     */
    public function equals(DiscountUse $other)
    {
        return $this->use === $other->use;
    }

    /**
     * Returns true if the use is once per entrant.
     *
     * @return bool
     */
    public function oncePerEntrant()
    {
        return $this->use == 'once-per-entrant';
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return (string) $this->use;
    }
}
