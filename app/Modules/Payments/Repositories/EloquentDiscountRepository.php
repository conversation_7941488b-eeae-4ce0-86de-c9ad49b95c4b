<?php

namespace AwardForce\Modules\Payments\Repositories;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Payments\Models\Discount;
use Carbon\Carbon;

class EloquentDiscountRepository extends Repository implements DiscountRepository
{
    public function __construct(Discount $discount)
    {
        $this->model = $discount;
    }

    /**
     * Returns a collection of discounts with related prices
     *
     * @param  int  $seasonId
     */
    public function getAllWithPrices($seasonId = null): \Platform\Database\Eloquent\Collection
    {
        return $this->getQuery()
            ->when($seasonId, function ($query) use ($seasonId) {
                $query->whereSeasonId($seasonId);
            })
            ->with(['price', 'categories'])
            ->withCount(['users'])
            ->get();
    }

    /**
     * Find and return a discount in the season given the code, or return null if none found.
     */
    public function getByCode(int $season, string $discountCode): ?Discount
    {
        return $this->getQuery()
            ->whereSeasonId($season)
            ->whereCode($discountCode)
            ->first();
    }

    /**
     * Find and return an active and valid discount in the season given the code, or return null if none found.
     */
    public function getActive(int $season, string $discountCode): ?Discount
    {
        $discount = $this->getQuery()
            ->whereSeasonId($season)
            ->whereCode($discountCode)
            ->whereActive(true)
            ->where(function ($query) {
                $query->where('expiry_date', '>=', Carbon::now())
                    ->orWhereNull('expiry_date');
            })->first();

        return $discount;
    }

    /**
     * Count all discounts in the specified season.
     */
    public function countInSeason(int $season): int
    {
        return $this->getQuery()
            ->whereSeasonId($season)
            ->count();
    }

    /**
     * Return all discounts for the specified season.
     */
    public function forSeason(int $season): \Platform\Database\Eloquent\Collection
    {
        return $this->getQuery()
            ->whereSeasonId($season)
            ->get();
    }

    /**
     * Returns the discounts associated with the category.
     *
     * @param  int  $categoryId
     * @return Collection
     */
    public function getFromCategory($categoryId)
    {
        return $this->getQuery()->whereHas('categories', function ($query) use ($categoryId) {
            $query->whereCategoryId($categoryId);
        })->get();
    }
}
