<?php

namespace AwardForce\Modules\Payments\Vendor\Bpoint\Requests;

use AwardForce\Modules\Payments\Vendor\Bpoint\Constants\Mode;

class Credentials
{
    private $username;
    private $password;
    private $merchantNumber;
    private $mode;

    public function __construct($username, $password, $merchantNumber, $mode = Mode::Live)
    {
        $this->username = $username;
        $this->password = $password;
        $this->merchantNumber = $merchantNumber;
        $this->mode = $mode;
    }

    public function getUsername()
    {
        return $this->username;
    }

    public function setUsername($username)
    {
        $this->username = $username;

        return $this;
    }

    public function getPassword()
    {
        return $this->password;
    }

    public function setPassword($password)
    {
        $this->password = $password;

        return $this;
    }

    public function getMerchantNumber()
    {
        return $this->merchantNumber;
    }

    public function setMerchantNumber($merchantNumber)
    {
        $this->merchantNumber = $merchantNumber;

        return $this;
    }

    public function getMode()
    {
        return $this->mode;
    }

    public function setMode($mode)
    {
        $this->mode = $mode;

        return $this;
    }
}
