<?php

namespace AwardForce\Modules\Payments\View;

use AwardForce\Modules\Accounts\Contracts\SupportedCurrencyRepository;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Payments\Repositories\TierRepository;
use AwardForce\Modules\Payments\Services\TierService;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Translator\Engine;

class Tiers extends View
{
    private $repository;
    private $fields;
    private $tiers;
    private $tierService;
    private $translator;
    private $supportedCurrencies;

    public function __construct(
        SettingRepository $repository,
        FieldRepository $fields,
        TierRepository $tiers,
        TierService $tierService,
        Engine $translator,
        SupportedCurrencyRepository $supportedCurrencies
    ) {
        $this->repository = $repository;
        $this->fields = $fields;
        $this->tiers = $tiers;
        $this->tierService = $tierService;
        $this->translator = $translator;
        $this->supportedCurrencies = $supportedCurrencies;
    }

    public function settings()
    {
        return $this->repository->getAllAsKeyValue();
    }

    public function supportedCurrencies()
    {
        return $this->supportedCurrencies->getAll()->sortBy('code');
    }

    public function tiers()
    {
        $tiers = $this->tierService->sortTiers($this->tiers->getAllWithPrices(SeasonFilter::getId()));

        return $this->translator->translate($tiers);
    }

    public function fields()
    {
        $fields = $this->fields->getByResource(Field::RESOURCE_FORMS);

        return $this->translator->shallow($fields);
    }

    public function relatedEntriesAmounts()
    {
        return json_decode(setting('related-entries-amounts', '[]'), true);
    }
}
