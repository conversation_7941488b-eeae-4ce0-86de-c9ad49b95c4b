<?php

namespace AwardForce\Modules\Files\Models;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Files\Enums\TranscriptionStatus;
use Tests\IntegratedTestCase;

class TranscriptionTest extends IntegratedTestCase
{
    public function testShouldUpdateTranscriptionStatus()
    {
        $file = $this->muffin(File::class);
        $transcription = new Transcription();
        $transcription->accountId = current_account_id();
        $transcription->userId = Consumer::user()->id;
        $transcription->fileId = $file->id;
        $transcription->status = TranscriptionStatus::InProgress;
        $transcription->save();

        $this->assertEquals($transcription->status, TranscriptionStatus::InProgress);

        $transcription->updateStatus(TranscriptionStatus::Completed);

        $this->assertEquals($transcription->status, TranscriptionStatus::Completed);
    }

    public function testExtractAccountGlobalIdFromJobName()
    {
        $jobNameLocal = 'local-9af4d702-3d2e-45be-96f9-7c7b97fb7d95-dOrApZrJ-811';
        $jobNameStaging = 'staging-9af4d702-3d2e-45be-96f9-7c7b97fb7d95-dOrApZrJ-811';
        $jobNameProd = 'prod-9af4d702-3d2e-45be-96f9-7c7b97fb7d95-dOrApZrJ-811';
        $jobNameNoPrefix = '9af4d702-3d2e-45be-96f9-7c7b97fb7d95-dOrApZrJ-811';
        $jobNameWrongAccountId = 'local-9af4d702-3d2e-FIVEE-96f9-7c7b97fb7d96-dOrApZrJ-811';

        $this->assertEquals('9af4d702-3d2e-45be-96f9-7c7b97fb7d95', Transcription::extractAccountGlobalId($jobNameLocal));
        $this->assertEquals('9af4d702-3d2e-45be-96f9-7c7b97fb7d95', Transcription::extractAccountGlobalId($jobNameStaging));
        $this->assertEquals('9af4d702-3d2e-45be-96f9-7c7b97fb7d95', Transcription::extractAccountGlobalId($jobNameProd));
        $this->assertEquals('9af4d702-3d2e-45be-96f9-7c7b97fb7d95', Transcription::extractAccountGlobalId($jobNameNoPrefix));
        $this->assertEquals('', Transcription::extractAccountGlobalId($jobNameWrongAccountId));
    }
}
