<?php

namespace AwardForce\Modules\Judging\Search\Leaderboard\Columns;

use AwardForce\Modules\Entries\Models\Entry;
use Consumer;
use HTML;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Updated implements Column
{
    /**
     * Title for the column - used for headers.
     *
     * @return string|HtmlString
     */
    public function title()
    {
        return trans('entries.table.columns.updated');
    }

    /**
     * Name of the column. This should be globally unique.
     */
    public function name(): string
    {
        return 'leaderboard.updated';
    }

    /**
     * Receives all the search filters available for search, and will state which ones it requires in order to be used.
     */
    public function dependencies(): Collection
    {
        return collect();
    }

    /**
     * Returns the name of the field in the query that should be present
     *
     * @return string|null
     */
    public function field()
    {
        return 'entries.submitted_at';
    }

    /**
     * Return the value required in $record.
     *
     * @param  Entry  $entry
     * @return mixed
     */
    public function value($assignment)
    {
        return $assignment->entry->updatedAt;
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @param  Entry  $entry
     * @return string|HtmlString
     */
    public function html($assignment)
    {
        return new HtmlString(HTML::relativeTime($assignment->entry->updatedAt, Consumer::dateLocale()));
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('none');
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * Return the priority of the column.
     */
    public function priority(): int
    {
        return 201;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return true;
    }
}
