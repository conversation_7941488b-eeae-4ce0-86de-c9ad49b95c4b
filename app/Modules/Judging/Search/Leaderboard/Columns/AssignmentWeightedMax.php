<?php

namespace AwardForce\Modules\Judging\Search\Leaderboard\Columns;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Assignments\Models\Assignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Search\Column;
use Platform\Search\Defaults;

class AssignmentWeightedMax implements Column
{
    public function title()
    {
        return trans('judging.export.heading.weighted-max');
    }

    public function name(): string
    {
        return 'vip_judging_leaderboard.assignment_weighted_max';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field()
    {
        return DB::raw('assignments.status, assignments.weighted_score,  assignments.weighted_total');
    }

    public function value($assignment)
    {
        if (in_array($assignment->status, [Assignment::STATUS_ABSTAINED, Assignment::STATUS_NONE]) || ! is_numeric($assignment->weightedScore)) {
            return '';
        }

        return $assignment->weightedTotal;
    }

    public function html($assignment)
    {
        return $this->value($assignment);
    }

    public function default(): Defaults
    {
        return new Defaults('export');
    }

    public function visible(): bool
    {
        return Consumer::can('view', 'ScoresAll');
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 100;
    }

    public function sortable(): bool
    {
        return true;
    }
}
