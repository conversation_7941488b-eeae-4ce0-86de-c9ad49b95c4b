<?php

namespace AwardForce\Modules\Judging\Search\Leaderboard\Columns;

use Illuminate\Support\Collection;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Conflict implements Column
{
    public function title()
    {
        return trans('judging.export.heading.conflict');
    }

    public function name(): string
    {
        return 'vip_judging_scores.conflict';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field()
    {
        return 'assignments.conflict_of_interest';
    }

    public function value($assignment)
    {
        return $assignment->conflictOfInterest ? trans('miscellaneous.toggles.yes') : '';
    }

    public function html($assignment)
    {
        return $this->value($assignment);
    }

    public function default(): Defaults
    {
        return new Defaults('export');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 100;
    }

    public function sortable(): bool
    {
        return false;
    }
}
