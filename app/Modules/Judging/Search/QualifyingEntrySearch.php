<?php

namespace AwardForce\Modules\Judging\Search;

use AwardForce\Library\Search\Filters\ActiveSeasonFilter;
use AwardForce\Library\Search\Linkable;
use AwardForce\Library\Search\Linker;
use AwardForce\Modules\Assignments\Models\AssignmentRepository;
use AwardForce\Modules\Entries\Search\Filters\CategorySearchFilter;
use AwardForce\Modules\Entries\Search\Filters\ChapterSearchFilter;
use AwardForce\Modules\Entries\Search\Filters\EntriesFieldSearchFilter;
use AwardForce\Modules\Judging\Search\Filters\AssignmentEntryFilter;
use AwardForce\Modules\Judging\Search\Filters\BadgeSearchFilter;
use AwardForce\Modules\Judging\Search\Filters\JudgeAssignmentsFilter;
use AwardForce\Modules\Judging\Search\Filters\JudgeDecisionFilter;
use AwardForce\Modules\Judging\Search\Filters\JudgingOrderFilter;
use AwardForce\Modules\Judging\Search\Filters\RoundAssignmentsFilter;
use AwardForce\Modules\Judging\Search\Filters\ScoreSetFilter;
use AwardForce\Modules\Judging\Search\Filters\ScoreSetModeFilter;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use Illuminate\Session\Store;
use Illuminate\Support\Facades\DB;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\KeywordFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\SearchFilter;
use Platform\Search\SearchFilterCollection;
use Platform\Search\SearchInterface;

class QualifyingEntrySearch implements Linkable, SearchInterface
{
    use Linker;

    /**
     * @var ScoreSet
     */
    protected $scoreSet;

    /**
     * @var array
     */
    protected $entryIds = [];

    /**
     * @var AssignmentRepository
     */
    protected $assignments;

    /**
     * @var Store
     */
    protected $session;

    public function __construct(ScoreSet $scoreSet, array $entryIds, AssignmentRepository $assignments, Store $session)
    {
        $this->scoreSet = $scoreSet;
        $this->entryIds = $entryIds;
        $this->assignments = $assignments;
        $this->session = $session;
    }

    /**
     * Setup the required filters necessary for executing an assignment search request, based on the $input provided.
     *
     * @param  bool  $paginate
     * @return mixed
     */
    public function fromInput(array $input = [], $paginate = true)
    {
        $input['score-set'] = $this->scoreSet->id;

        $this->saveInput($input);

        $filters = new SearchFilterCollection;

        $filters->add(app(ActiveSeasonFilter::class))
            ->add(new PaginationFilter($input, $this->scoreSet->entryListLength))
            ->add(app(JudgeAssignmentsFilter::class))
            ->add(app(RoundAssignmentsFilter::class))
            ->add(new AssignmentEntryFilter($input))
            ->add(new ScoreSetModeFilter(ScoreSet::MODE_QUALIFYING, 'assignments'))
            ->add(new GroupingFilter('assignments.entry_id'))
            ->add(new ScoreSetFilter($input, app(ScoreSetRepository::class)))
            ->add(new JudgeDecisionFilter($input))
            ->add(new CategorySearchFilter($input))
            ->add(new ChapterSearchFilter($input))
            ->add(new BadgeSearchFilter($this->scoreSet->id, $input, 'entry.badges'))
            ->add(EntriesFieldSearchFilter::forScoreSet($input));

        $filters->add(new IncludeFilter([
            'entry',
            'entry.attachments.file',
            'entry.badges',
            'entry.category',
            'entry.chapter',
            'entry.tags',
        ]));

        $filters->add(new ColumnFilter(
            'assignments.id',
            'assignments.entry_id as entry_id',
            'entries.slug as entry_slug',
            'entries.category_id as category_id',
            'entries.title as title',
            DB::raw('GROUP_CONCAT(DISTINCT assignment_decisions.decision) as current_decision')
        ));

        if (isset($input['keywords'])) {
            if ($this->scoreSet->displayEntryName) {
                $filters->add(new KeywordFilter($input['keywords'], ['entries.title', 'entries.local_id']));
            } else {
                $filters->add(new KeywordFilter($input['keywords'], ['entries.local_id']));
            }
        }

        $filters->add($this->orderFilter($input));

        return $this->assignments->getByFilters($filters, $paginate);
    }

    /**
     * Return Session store instance, for use by the Linker trait.
     */
    protected function session(): Store
    {
        return $this->session;
    }

    /**
     * @return SearchFilter
     */
    protected function orderFilter(array $input)
    {
        if (empty($input['order'])) {
            return JudgingOrderFilter::forScoreSet($this->scoreSet, $this->entryIds);
        }

        $orderFilter = OrderFilter::byInput($input);
        $orderFilter->setDefaultField('assignments.slug');
        $orderFilter->setFieldMap([
            'id' => 'entries.local_id',
            'title' => 'entries.title',
            'decision' => 'current_decision',
        ]);
        $orderFilter->uniqueColumn('assignments.id');

        return $orderFilter;
    }
}
