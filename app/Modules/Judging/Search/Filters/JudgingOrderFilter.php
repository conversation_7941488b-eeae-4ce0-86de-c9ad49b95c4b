<?php

namespace AwardForce\Modules\Judging\Search\Filters;

use AwardForce\Library\Search\Filters\RandomOrderFilter;
use AwardForce\Modules\Judging\Exceptions\UnknownJudgingOrderMode;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetCollection;
use Platform\Search\Filters\SearchFilter;

class JudgingOrderFilter
{
    public static function forScoreSet(ScoreSet $scoreSet, ?array $entries = null): SearchFilter
    {
        $selectedMode = $scoreSet->orderControl;

        if (! in_array($selectedMode, ScoreSet::$orderModes)) {
            throw new UnknownJudgingOrderMode($selectedMode);
        }

        return self::{camel_case($selectedMode)}($scoreSet, $entries);
    }

    public static function forScoreSets(ScoreSetCollection $scoreSets, ?array $entries = null): SearchFilter
    {
        $scoreSet = new ScoreSet;
        $scoreSet->id = $scoreSets->sum->id;
        $scoreSet->orderControl = $scoreSets->orderControl();

        return self::forScoreSet($scoreSet, $entries);
    }

    private static function random(ScoreSet $scoreSet, ?array $entries = null)
    {
        /**
         * Voting uses it's own random ordering for caching reasons (see VoteAggregator). Here we just apply an initial
         * ordering by assignment id.
         */
        if ($scoreSet->isVoting()) {
            return new class implements SearchFilter
            {
                public function applyToEloquent($query)
                {
                    return $query->orderBy('assignments.id');
                }
            };
        }

        $filter = app(RandomOrderFilter::class)
            ->addRandomness($scoreSet->id);

        if ($entries) {
            return $filter->byField('assignments.entry_id', $entries);
        }

        return $filter;
    }

    private static function entryId()
    {
        return new class implements SearchFilter
        {
            public function applyToEloquent($query)
            {
                return $query->orderBy('assignments.entry_id', 'ASC');
            }
        };
    }

    private static function categoryEntryId()
    {
        return new class extends CategoryNameEntriesOrderFilter
        {
            protected function extendQuery($query)
            {
                return $query->orderBy('order_entries.id', 'ASC');
            }
        };
    }

    private static function entryName()
    {
        return new class implements SearchFilter
        {
            public function applyToEloquent($query)
            {
                return $query->join('entries as order_entries', 'order_entries.id', '=', 'assignments.entry_id')
                    ->orderBy('order_entries.title', 'ASC');
            }
        };
    }

    private static function categoryEntryName()
    {
        return new class extends CategoryNameEntriesOrderFilter
        {
            protected function extendQuery($query)
            {
                return $query->orderBy('order_entries.title', 'ASC');
            }
        };
    }
}
