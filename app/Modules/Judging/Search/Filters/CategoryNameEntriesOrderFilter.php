<?php

namespace AwardForce\Modules\Judging\Search\Filters;

use AwardForce\Library\Facades\CurrentLocale;
use Platform\Search\Filters\SearchFilter;

class CategoryNameEntriesOrderFilter implements SearchFilter
{
    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        $query = $query->join('entries as order_entries', 'order_entries.id', '=', 'assignments.entry_id')
            ->join('categories as order_categories', 'order_categories.id', '=', 'order_entries.category_id')
            ->leftJoin('translations as order_category_names', function ($join) {
                return $join->on('order_category_names.foreign_id', '=', 'order_categories.id')
                    ->where('order_category_names.resource', '=', 'Category')
                    ->where('order_category_names.language', CurrentLocale::strictDefault()->code)
                    ->where('order_category_names.field', '=', 'name');
            })
            ->leftJoin('translations as order_parent_category_names', function ($join) {
                return $join->on('order_parent_category_names.foreign_id', '=', 'order_categories.parent_id')
                    ->where('order_parent_category_names.resource', '=', 'Category')
                    ->where('order_parent_category_names.language', CurrentLocale::strictDefault()->code)
                    ->where('order_parent_category_names.field', '=', 'name');
            })
            ->orderBy('order_parent_category_names.value', 'ASC')
            ->orderBy('order_category_names.value', 'ASC');

        return $this->extendQuery($query);
    }

    /**
     * @return mixed
     */
    protected function extendQuery($query)
    {
        return $query;
    }
}
