<?php

namespace AwardForce\Modules\Judging\View;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Categories\Services\CategoryDivisions;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Judging\Services\DefaultConfigurationGenerator;
use AwardForce\Modules\Judging\Services\Modes;
use AwardForce\Modules\Panels\Traits\ManagesRoles;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Tags\Contracts\TagRepository;
use Illuminate\Support\Collection;
use Platform\View\View;

class JudgingFastStart extends View
{
    use ManagesRoles;

    /** @var Modes */
    private $modes;

    /** @var ScoreSetRepository */
    private $scoreSets;

    /** @var CategoryRepository */
    private $categories;

    /** @var CategoryDivisions */
    private $categoryDivisions;

    /** @var ChapterRepository */
    private $chapters;

    /** @var TagRepository */
    private $tags;

    /** @var RoundRepository */
    private $rounds;

    public function __construct(
        Modes $modes,
        ScoreSetRepository $scoreSets,
        CategoryRepository $categories,
        CategoryDivisions $categoryDivisions,
        ChapterRepository $chapters,
        TagRepository $tags,
        RoundRepository $rounds
    ) {
        $this->modes = $modes;
        $this->scoreSets = $scoreSets;
        $this->categories = $categories;
        $this->categoryDivisions = $categoryDivisions;
        $this->chapters = $chapters;
        $this->tags = $tags;
        $this->rounds = $rounds;
    }

    public function steps(): array
    {
        return [
            ['id' => 'mode', 'name' => trans('judging.fast_start.steps.mode')],
            ['id' => 'scoring', 'name' => trans('judging.fast_start.steps.scoring')],
            ['id' => 'entries', 'name' => trans('judging.fast_start.steps.entries')],
            ['id' => 'judges', 'name' => trans('judging.fast_start.steps.judges')],
            ['id' => 'rounds', 'name' => trans('judging.fast_start.steps.rounds')],
            ['id' => 'complete', 'name' => trans('judging.fast_start.steps.complete')],
        ];
    }

    public function allModes(): array
    {
        return $this->modes->all();
    }

    public function availableModes(): array
    {
        return $this->modes->available();
    }

    public function judgingScoreSets()
    {
        return translate($this->scoreSets->getForForm(FormSelector::selectedOrDefault()->id))
            ->map(function ($scoreSet) {
                return [
                    'id' => $scoreSet->id,
                    'name' => $scoreSet->name,
                    'mode' => $scoreSet->mode,
                ];
            })
            ->groupBy('mode')
            ->toArray();
    }

    public function season(): array
    {
        $season = SeasonFilter::selectedOrActive();

        return [
            'id' => $season->id,
            'status' => $season->status,
        ];
    }

    public function categories(): array
    {
        $categories = translate($this->categories->getAllForForm(FormSelector::selectedOrDefault()->id));

        return $this->categoryDivisions->prepareCategories($categories->toHierarchy());
    }

    public function chapters(): Collection
    {
        $chapters = translate($this->chapters->getAllForForm(FormSelector::selectedOrDefault()));

        $visibleChapterIds = Consumer::isChapterManager() ?
             $this->chapters->getByManager(Consumer::user()->id)->pluck('id')->toArray() :
             $chapters->pluck('id')->toArray();

        return $chapters->map(function (Chapter $chapter) use ($visibleChapterIds) {
            return [
                'id' => $chapter->id,
                'name' => lang($chapter, 'name'),
                'disabled' => ! in_array($chapter->id, $visibleChapterIds),
            ];
        });
    }

    public function tags(): Collection
    {
        return $this->tags->getForSeason(SeasonFilter::getId());
    }

    public function rounds(): array
    {
        return translate($this->rounds->getJudgingForForm(FormSelector::selectedOrDefault()->id))
            ->map(function ($round) {
                return [
                    'id' => $round->id,
                    'slug' => (string) $round->slug,
                    'name' => lang($round, 'name'),
                    'startsAt' => $this->date($round->startsAt, $round->startsTz),
                    'startsTz' => $round->startsTz,
                    'endsAt' => $this->date($round->endsAt, $round->endsTz),
                    'endsTz' => $round->endsTz,
                    'createdAt' => (string) $round->createdAt,
                ];
            })->toArray();
    }

    private function date($date, $timezone): ?string
    {
        return $date ? (string) convert_date_to_timezone($date, $timezone)->format('Y-m-d H:i') : null;
    }

    public function votingScoreSetTemplate(): array
    {
        return DefaultConfigurationGenerator::forScoreSet('', ScoreSet::MODE_VOTING);
    }

    public function topPickScoreSetTemplate(): array
    {
        return DefaultConfigurationGenerator::forScoreSet('', ScoreSet::MODE_TOP_PICK);
    }

    public function galleryScoreSetTemplate(): array
    {
        return DefaultConfigurationGenerator::forScoreSet('', ScoreSet::MODE_GALLERY);
    }

    public function scoringCriterionTemplate(): array
    {
        return DefaultConfigurationGenerator::forScoringCriterionView('');
    }

    public function roundTemplate(): array
    {
        return DefaultConfigurationGenerator::forRound('');
    }

    public function routes(): array
    {
        return routes_for_vue([
            'fast-start.round.validate',
            'fast-start.scoring-criterion.validate',
            'fast-start.save',
            'judging-dashboard.index',
        ]);
    }

    public function translations(): array
    {
        return translations_for_vue(Consumer::languageCode(), [
            'assignments.form.judges.capped',
            'buttons.add',
            'buttons.back',
            'buttons.cancel',
            'buttons.delete',
            'buttons.edit',
            'buttons.finish',
            'buttons.next',
            'buttons.save',
            'buttons.search',
            'fields.preview.error',
            'judging.fast_start',
            'judging.preview',
            'multiselect.randomizer.show',
            'multiselect.randomizer.helpicon-judges',
            'miscellaneous.slider.current_value',
            'multiselect.select_all',
            'panels.form.advanced',
            'panels.form.categories',
            'panels.form.chapters',
            'panels.form.judges',
            'panels.form.reset',
            'panels.form.role.label',
            'panels.form.selected-judges.header',
            'panels.form.tags',
            'panels.modes',
            'qualifying.controls.fail',
            'qualifying.controls.pass',
            'rounds.form.ends.label',
            'rounds.form.starts.label',
            'score-set.form.max-votes-per-category.label',
            'score-set.form.max-votes-per-entry.label',
            'score-set.form.max-votes-per-user.label',
            'score-set.form.top-pick-mode',
            'score-set.form.top-pick-preferences.label',
            'score-set.form.top-pick-winners.label',
            'score-set.form.vote_limits',
            'score-set.form.votes-revokable.label',
            'score-set.form.votes-visible.label',
            'score-set.modes',
            'score-set.titles.voting',
            'scoring-criteria.form.label',
            'scoring-criteria.form.maximum_score',
            'scoring-criteria.form.minimum_score',
            'scoring-criteria.form.scoring_increment',
            'scoring-criteria.form.weight',
            'search.nothing-found',
            'users.form.email.label',
            'users.form.email.multiple_emails',
            'validation.multiple_emails',
            'voting.controls.vote',
            'voting.controls.voted',
            'shared.learn_more',
            'miscellaneous.optional',
        ]);
    }
}
