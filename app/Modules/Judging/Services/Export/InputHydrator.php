<?php

namespace AwardForce\Modules\Judging\Services\Export;

use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class InputHydrator
{
    /**
     * @var \AwardForce\Modules\ScoreSets\Models\ScoreSetRepository
     */
    private $scoreSets;

    /**
     * ExportLeaderboardValidator constructor.
     */
    public function __construct(ScoreSetRepository $scoreSets)
    {
        $this->scoreSets = $scoreSets;
    }

    /**
     * Add season to input
     *
     * @param  string  $mode
     * @return array
     */
    public function addSeason(array $input, $mode)
    {
        if (empty($input['score-set'])) {
            throw (new ModelNotFoundException)->setModel(ScoreSet::class);
        }

        $scoreSet = $this->scoreSets->requireById($input['score-set']);

        if ($scoreSet->mode != $mode) {
            throw (new ModelNotFoundException)->setModel(ScoreSet::class);
        }

        $input['seasonId'] = $scoreSet->seasonId;

        return $input;
    }
}
