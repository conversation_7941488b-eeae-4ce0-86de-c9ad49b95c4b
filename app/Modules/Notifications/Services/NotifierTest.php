<?php

namespace AwardForce\Modules\Notifications\Services;

use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Notifications\Services\Recipients\CollaboratorRecipient;
use AwardForce\Modules\Notifications\Services\Recipients\Recipients;
use AwardForce\Modules\Notifications\Services\Recipients\UserRecipient;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class NotifierTest extends BaseTestCase
{
    use Laravel;

    private Courier|m\MockInterface $courier;
    private Notifier $notifier;

    public function init()
    {
        $this->courier = m::mock(Courier::class);
        app()->instance(Courier::class, $this->courier);
        $this->notifier = app(Notifier::class);
    }

    public function testOverridesCollaboratorsNameWhenIsCollaboratorRecipient()
    {
        $user = new User;
        $user->firstName = 'Luke';
        $user->lastName = 'Skywalker';
        $collaboratorRecipient = new CollaboratorRecipient($user, 'en_GB');
        $recipients = new Recipients([$collaboratorRecipient]);

        $this->courier->shouldReceive('deliver')
            ->once()
            ->with(m::type(Notification::class), $collaboratorRecipient, m::on(function (array $data) use ($user) {
                $this->assertEquals($user->firstName, $data['first_name']);
                $this->assertEquals($user->lastName, $data['last_name']);

                return true;
            }));

        $this->notifier->notify($recipients, ['first_name' => 'ShouldChange', 'last_name' => 'AlsoShouldChange'], new Notification);
    }

    public function testDoesNotOverrideNamesWhenIsNotCollaboratorRecipient()
    {
        $user = new User;
        $user->firstName = 'Luke';
        $user->lastName = 'Skywalker';
        $userRecipient = new UserRecipient($user, 'en_GB');
        $recipients = new Recipients([$userRecipient]);

        $data = ['first_name' => $originalFirstName = 'ShouldChange', 'last_name' => $originalLastName = 'AlsoShouldChange'];
        $this->courier->shouldReceive('deliver')
            ->once()
            ->with(m::type(Notification::class), $userRecipient, m::on(function (array $data) use ($originalFirstName, $originalLastName) {
                return $data['first_name'] === $originalFirstName &&
                    $data['last_name'] === $originalLastName;
            })
            );

        $this->notifier->notify($recipients, $data, new Notification);
    }

    public function testItDoesNotAddNamesIfTheyAreNotPresent()
    {
        $user = new User;
        $user->firstName = 'Luke';
        $user->lastName = 'Skywalker';
        $collaboratorRecipient = new CollaboratorRecipient($user, 'en_GB');
        $recipients = new Recipients([$collaboratorRecipient]);

        $this->courier->shouldReceive('deliver')
            ->once()
            ->with(m::type(Notification::class), $collaboratorRecipient, m::on(function (array $data) {
                $this->assertArrayNotHasKey('first_name', $data);
                $this->assertArrayNotHasKey('last_name', $data);

                return true;
            }));

        $data = []; // No first_name or last_name in data
        $this->notifier->notify($recipients, $data, new Notification);
    }
}
