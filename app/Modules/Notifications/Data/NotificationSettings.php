<?php

namespace AwardForce\Modules\Notifications\Data;

use Assert\Assertion;
use AwardForce\Library\Values\Services\Transformable;
use AwardForce\Library\Values\Services\Transformer;
use Illuminate\Contracts\Database\Eloquent\Castable;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Model;

class NotificationSettings implements Arrayable, Castable, Transformable
{
    use Transformer;

    public function __construct(public readonly bool $sendToCollaborators = false)
    {

    }

    public function toArray(): array
    {
        return get_object_vars($this);
    }

    public static function castUsing(array $arguments)
    {
        return new class implements CastsAttributes
        {
            public function get(Model $model, string $key, mixed $value, array $attributes): NotificationSettings
            {
                $data = json_decode($value ?? '{}', true);

                return NotificationSettings::transform($data);
            }

            public function set(Model $model, string $key, mixed $value, array $attributes): string
            {
                Assertion::isInstanceOf($value, NotificationSettings::class);

                return json_encode($value->toArray());
            }
        };
    }
}
