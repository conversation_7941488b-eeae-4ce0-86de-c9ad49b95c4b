<?php

namespace AwardForce\Modules\Panels\Commands;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Identity\Users\Services\UserPermissionsService;
use Illuminate\Support\Arr;
use Platform\Authorisation\FeatureRoles\Judge;

class AddConsumerToPanel
{
    public function __construct(private array $panelConfiguration)
    {
    }

    public function handle(UserPermissionsService $permissions)
    {
        $judgeIds = Arr::get($this->panelConfiguration, 'judgeIds', []);
        $roleIds = [];

        if ($this->isRoleBased()) {
            $roleIds = Arr::get($this->panelConfiguration, 'roleIds', []);
        } else {
            if (! Consumer::isJudge()) {
                $judgeRoles = $permissions->getRolesWithPermissions(Judge::permissions());
                $roleIds = $judgeRoles->pluck('id')->toArray();
            }
            $judgeIds = array_merge($judgeIds, [Consumer::id()]);
        }
        Consumer::user()->grantRoles($roleIds);

        return $judgeIds;
    }

    private function isRoleBased(): bool
    {
        return (bool) Arr::get($this->panelConfiguration, 'roleIds');
    }
}
