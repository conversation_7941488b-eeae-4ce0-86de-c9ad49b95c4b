<?php

namespace AwardForce\Modules\ScoreSets\Search;

use AwardForce\Library\Search\Columns\Marker;
use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Library\Search\Filters\TagSearchFilter;
use AwardForce\Modules\Comments\Models\CommentRepository;
use AwardForce\Modules\Comments\Search\Filters\AssignmentCommentFilter;
use AwardForce\Modules\Ecommerce\Orders\Search\Columns\OrderItems\EntryName;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Search\Filters\CategorySearchFilter;
use AwardForce\Modules\Entries\Search\Filters\ChapterSearchFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryStateFilter;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Judging\Search\Filters\ConsensusSearchFilter;
use AwardForce\Modules\ScoreSets\Search\Columns\Comment;
use AwardForce\Modules\ScoreSets\Search\Columns\CommenterEmail;
use AwardForce\Modules\ScoreSets\Search\Columns\CommenterName;
use AwardForce\Modules\ScoreSets\Search\Columns\CommenterSlug;
use AwardForce\Modules\ScoreSets\Search\Columns\EntryId;
use AwardForce\Modules\ScoreSets\Search\Columns\ScoreSetName;
use AwardForce\Modules\ScoreSets\Search\Enhancers\EntryEnhancer;
use AwardForce\Modules\ScoreSets\Search\Enhancers\ScoreSetEnhancer;
use AwardForce\Modules\ScoreSets\Search\Filters\ScoreSetFilter;
use Illuminate\Support\Arr;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Columnator as ColumnatorColumn;
use Platform\Search\Columns\Created;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\KeywordFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;

class ScoreSetCommentsColumnator extends Columnator
{
    /**
     * @return mixed
     */
    protected function baseColumns(): Columns
    {
        return new Columns([
            new Marker,
            new ColumnatorColumn('score_set_comments.search'),
            new EntryId,
            new EntryName,
            new Comment,
            new CommenterName,
            new CommenterEmail,
            new CommenterSlug,
            new ScoreSetName,
            Created::forResource('comments', consumer()->dateLocale()),
            Updated::forResource('comments', consumer()->dateLocale()),
        ]);
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = new Dependencies;
        $dependencies->add(new AssignmentCommentFilter());
        $dependencies->add((new ColumnFilter(...$this->columns($view)))->with('comments.id', 'comments.slug', 'comments.tags'));
        $dependencies->add(new IncludeFilter(['user', 'assignments', 'assignments.entry', 'assignments.entry.tags', 'assignments.commentLinks']));
        $dependencies->add(new TranslatedColumnSearchFilter($this->availableColumns(), 'ScoreSetComment', current_account_id(), $this->input));
        $dependencies->add(new GroupingFilter('comments.id'));
        $dependencies->add(new ChapterSearchFilter($this->input));
        $dependencies->add(new CategorySearchFilter($this->input, true));
        $dependencies->add(new TagSearchFilter($this->input, app(EntryRepository::class), [Entry::class, Allocation::class], [Allocation::class => 'entry_id']));
        $dependencies->add(new EntryStateFilter($this->input));
        $dependencies->add(new SeasonalFilter(Arr::get($this->input, 'season'), 'assignments.season_id'));
        $dependencies->add(new ConsensusSearchFilter($this->input));
        $dependencies->add(new EntryStateFilter($this->input));
        $dependencies->add(OrderFilter::fromColumns($this->columns($view), $this->input, 'comments.updated')->uniqueColumn('comments.id'));
        $dependencies->add(new ScoreSetFilter(Arr::get($this->input, 'score-set')));
        $dependencies->add(KeywordFilter::fromInput($this->input, ['entries.title', 'entries.local_id']));

        //Enhancers
        $dependencies->add(app(EntryEnhancer::class));
        $dependencies->add(new ScoreSetEnhancer(Arr::get($this->input, 'score-set')));

        return $dependencies;
    }

    /**
     * @return string|null
     */
    public function resource()
    {
        return 'ScoreSets';
    }

    public static function key(): string
    {
        return 'score_set_comments.search';
    }

    public static function exportKey(): string
    {
        return 'score_set_comments.export';
    }

    public function repository(): Repository
    {
        return app(CommentRepository::class);
    }
}
