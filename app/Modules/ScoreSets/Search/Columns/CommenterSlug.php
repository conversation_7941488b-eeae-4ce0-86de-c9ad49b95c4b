<?php

namespace AwardForce\Modules\ScoreSets\Search\Columns;

use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;
use Platform\Search\Defaults;
use Platform\Search\Filters\IncludeFilter;

class CommenterSlug implements Column
{
    /**
     * Title for the column - used for headers.
     *
     * @return string|HtmlString
     */
    public function title()
    {
        return trans('score-set.export.heading.code');
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'score-set-comments.commenter-slug';
    }

    /**
     * Receives all the search filters available for search, and will state which ones it requires in order to be used.
     */
    public function dependencies(): Collection
    {
        return collect([
            IncludeFilter::class,
        ]);
    }

    /**
     * When it comes to field columns, the slug is a unique string value that can be used for fetching the field from the query.
     *
     * @return string|null
     */
    public function field()
    {
        return 'comments.user_id';
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record)
    {
        return $record->user->slug ?? '';
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|HtmlString
     */
    public function html($record)
    {
        return $this->value($record);
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('export');
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * Return the priority of the column.
     */
    public function priority(): int
    {
        return 30;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return false;
    }
}
