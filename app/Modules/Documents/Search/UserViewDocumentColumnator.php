<?php

namespace AwardForce\Modules\Documents\Search;

use AwardForce\Library\Search\Actions\DeleteAction;
use AwardForce\Library\Search\Actions\DownloadAction;
use AwardForce\Library\Search\Columns\ActionOverflow;
use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Modules\Documents\Search\Actions\EditDocumentAction;
use AwardForce\Modules\Documents\Search\Columns\Allocation;
use AwardForce\Modules\Documents\Search\Columns\Entry;
use AwardForce\Modules\Documents\Search\Columns\FileType;
use AwardForce\Modules\Documents\Search\Columns\Name;
use AwardForce\Modules\Documents\Search\Columns\Shared;
use AwardForce\Modules\Documents\Search\Columns\UserCreated;
use AwardForce\Modules\Documents\Search\Filters\UserAccountFilter;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;

class UserViewDocumentColumnator extends UserDocumentColumnator
{
    protected function baseColumns(): Columns
    {
        return new Columns([
            new ReactiveMarker,
            $this->actionOverflow(),
            new Name,
            new FileType,
            new Shared,
            new Entry,
            new Allocation,
            new UserCreated,
        ]);
    }

    public function availableDependencies(Defaults $view): Dependencies
    {
        return parent::availableDependencies($view)
            ->add(new UserAccountFilter($this->input['entrant']));
    }

    public static function key(): string
    {
        return 'document.user.search';
    }

    public static function exportKey(): string
    {
        return 'document.user.export';
    }

    protected function actionOverflow(): ActionOverflow
    {
        return (new ActionOverflow($this->key()))
            ->addAction(new DownloadAction('document', $this->resource()))
            ->addAction(new EditDocumentAction('document', $this->resource()))
            ->addAction(new DeleteAction('document', $this->resource()));
    }
}
