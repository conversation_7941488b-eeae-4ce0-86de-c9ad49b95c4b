<?php

namespace AwardForce\Modules\Integrations\Marketplace\Views;

use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Authentication\Services\Emulator\Facades\JediEmulator;
use AwardForce\Modules\Integrations\Marketplace\Services\MarketplaceManager;
use Platform\View\View;

class MarketplaceView extends View
{
    public function __construct(private MarketplaceManager $marketplaceManager)
    {
        VueData::registerRoutes([
            'marketplace.event',
        ]);
    }

    public function jwt()
    {
        return $this->marketplaceManager->jwt();
    }

    public function prismaticUrl(): string
    {
        return config('services.prismatic.'.$this->brand().'_url', '');
    }

    public function apiKey(): string
    {
        return $this->marketplaceManager->apiKey()->value;
    }

    public function brand(): string
    {
        return current_account_brand();
    }

    public function langCode(): string
    {
        return consumer()->languageCode();
    }

    public function connectionKey()
    {
        return brand_config('marketplace.connection_key');
    }

    public function showExtraInfo(): bool
    {
        return JediEmulator::active();
    }

    public function apiUrl(): string
    {
        return enforce_url_https(app()->environment(['staging', 'local']) ?
            config('services.prismatic.api_base_url') :
            config('api.domain')
        );
    }
}
