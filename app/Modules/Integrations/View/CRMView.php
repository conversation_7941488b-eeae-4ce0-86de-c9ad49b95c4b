<?php

namespace AwardForce\Modules\Integrations\View;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Integrations\Data\IntegrationRepository;
use AwardForce\Modules\Integrations\Exceptions\ClientAuthException;
use AwardForce\Modules\Integrations\Exceptions\InvalidUser;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use Tectonic\LaravelLocalisation\Translator\Engine;

class CRMView extends IntegrationView
{
    /** @var FieldRepository */
    private $fields;

    /** @var IntegrationRepository */
    private $integrations;

    /** @var SeasonFilterService */
    private $season;

    /** @var Engine */
    private $translator;

    /** @var TabRepository */
    private $tabs;

    public function __construct(
        FieldRepository $fields,
        IntegrationRepository $integrations,
        SeasonFilterService $season,
        Engine $translator,
        TabRepository $tabs
    ) {
        $this->fields = $fields;
        $this->integrations = $integrations;
        $this->season = $season;
        $this->translator = $translator;
        $this->tabs = $tabs;
    }

    public function parents()
    {
        $integrations = $this->integrations
            ->getFromSeasonAndDriver($this->season->getId(), object_get($this->integration, 'driver', ''))
            ->pluck('name', 'id')
            ->all();

        unset($integrations[$this->integration->id]);

        return ['' => ''] + $integrations;
    }

    public function objects()
    {
        if ($this->integration->configurable()) {
            try {
                $sessionKey = $this->integration->sessionKey('objects');

                return $this->integration->adapter()->getAvailableObjects($sessionKey);
            } catch (ClientAuthException|InvalidUser $ex) {
                $this->integration->invalidateAuth();
            }
        }
    }

    public function userFields()
    {
        $fields = $this->fields->getAllBySeasonAndResource($this->season->getId(), Field::RESOURCE_USERS);

        return $this->translator->shallow($fields);
    }

    public function standardUserFields()
    {
        $fields = ['email', 'firstName', 'lastName'];

        return collect($fields)->mapWithKeys(function ($field) {
            return [$field => trans("integrations.form.standard-fields.user.{$field}")];
        })->all();
    }

    public function userUniqueIdentifier()
    {
        $userFields = array_merge($this->standardUserFields, for_select_sorted($this->userFields, ['slug', 'title']));

        return [
            '' => '',
            trans('integrations.form.standard-fields.user.heading') => $userFields,
        ];
    }

    public function paymentUniqueIdentifier()
    {
        $userFields = array_merge($this->standardUserFields, for_select_sorted($this->userFields, ['slug', 'title']));

        return [
            '' => '',
            'memberNumber' => trans('integrations.form.crm.payment.fields.memberNumber'),
            trans('integrations.form.standard-fields.user.heading') => $userFields,
        ];
    }

    public function entryFields()
    {
        $fields = $this->fields->getAllBySeasonAndResource($this->season->getId(), Field::RESOURCE_FORMS);

        return $this->translator->shallow($fields);
    }

    public function standardEntryFields()
    {
        $fields = ['season', 'chapter', 'category', 'entryName', 'entryId', 'entryCode'];

        return collect($fields)->mapWithKeys(function ($field) {
            return [$field => trans("integrations.form.standard-fields.entry.{$field}")];
        })->all();
    }

    public function entryUniqueIdentifier()
    {
        $entryFields = array_merge($this->standardEntryFields, for_select_sorted($this->entryFields, ['slug', 'title']));
        $userFields = array_merge($this->standardUserFields, for_select_sorted($this->userFields, ['slug', 'title']));

        return [
            '' => '',
            trans('integrations.form.standard-fields.entry.heading') => $entryFields,
            trans('integrations.form.standard-fields.user.heading') => $userFields,
        ];
    }

    public function contributorTabs()
    {
        $tabs = $this->tabs->forResourceType(Tab::RESOURCE_ENTRIES, Tab::TYPE_CONTRIBUTORS, $this->season->get());

        return $this->translator->shallow($tabs);
    }

    public function contributorFields()
    {
        $fields = $this->fields->getAllBySeasonAndResource($this->season->getId(), Field::RESOURCE_CONTRIBUTORS);

        return $this->translator->shallow($fields)->groupBy('tab_id');
    }
}
