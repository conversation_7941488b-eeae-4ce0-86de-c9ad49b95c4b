<?php

namespace AwardForce\Modules\Holocron\View;

use AwardForce\Modules\Billing\Services\BillingPortalGateway;
use AwardForce\Modules\Holocron\Services\Guides;
use AwardForce\Modules\Holocron\Services\Tours;
use Consumer;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Factory\MuffinFactory;

class ToursAndGuidesViewTest extends BaseTestCase
{
    use Database,
        Laravel,
        MuffinFactory;

    private ToursAndGuidesView $view;
    private BillingPortalGateway $billingPortalGateway;

    protected function init(): void
    {
        $this->billingPortalGateway = $this->mock(BillingPortalGateway::class);
        $this->view = app(ToursAndGuidesView::class);
        app()->instance(BillingPortalGateway::class, $this->billingPortalGateway);
        current_account()->subscriptionId = 'theSubscriptionId';
        current_account()->subscriptionProvider = 'awardforce';
    }

    #[TestWith(['goodgrants', 'intro', 'Intro', ['Premium', 'Enterprise']])]
    #[TestWith(['awardforce', 'growth', 'Growth', ['Pro', 'Premier']])]
    public function testItReturnsSubscription(string $brand, string $plan, string $planName, array $plans): void
    {
        $fakeSubscription = [
            'plans' => $plans,
            'planName' => $planName,
            'id' => 'theSubscriptionId',
        ];

        current_account()->plan = $plan;
        current_account()->brand = $brand;
        current_account()->save();

        $view = $this->view = new ToursAndGuidesView(
            app(Tours::class),
            app(Guides::class),
        );

        $this->assertEquals($fakeSubscription, $view->subscription());
    }

    public function testItReturnsPlanNameProperly(): void
    {
        current_account()->plan = 'ggenterprise';
        current_account()->save();

        $this->assertEquals('Enterprise', $this->view->planName());
    }

    public function testItReturnsPlanNameForGrowthPlan(): void
    {
        current_account()->plan = 'growth';
        current_account()->save();

        $this->assertEquals('Growth', $this->view->planName());
    }

    public function testItReturnPlansPage(): void
    {
        $this->assertEquals(
            config('links.'.current_account_brand().'.plan_pricing'),
            $this->view->plansPage()
        );

        current_account()->brand = 'goodgrants';
        current_account()->save();

        $this->assertEquals(
            config('links.'.current_account_brand().'.plan_pricing'),
            $this->view->plansPage()
        );
    }

    public function testItCanNotUpgradePlanWhenAccountIsTrial(): void
    {
        current_account()->trialEndDate = now();
        current_account()->save();

        $this->assertFalse($this->view->canUpgradePlan());
    }

    public function testItCanNotUpgradePlanWhenThereAreNotSubscriptionData(): void
    {
        current_account()->subscriptionId = null;
        current_account()->subscriptionProvider = null;
        current_account()->save();

        $this->assertFalse($this->view->canUpgradePlan());
    }

    public function testItCanNotUpgradePlanIfConsumerIsNotOwnerOrProgramManager(): void
    {
        Consumer::shouldReceive('isOwner')->andReturnFalse();

        $this->assertFalse($this->view->canUpgradePlan());
    }

    public function testItCanNotUpgradePlanWhenThereAreNoAvailableSubscriptionPlans(): void
    {
        current_account()->plan = 'premier';
        current_account()->save();

        $this->assertFalse($this->view->canUpgradePlan());
    }

    public function testItCanUpgradePlan(): void
    {
        current_account()->plan = 'growth';
        current_account()->save();
        Consumer::shouldReceive('isOwner')->andReturnTrue();

        $this->assertTrue($this->view->canUpgradePlan());
    }
}
