<?php

namespace AwardForce\Console\Commands;

use AwardForce\Modules\PaymentMethods\Models\PaymentMethod;
use Illuminate\Console\Command;

class RemovePhantomPaymentMethodsCommand extends Command
{
    use RunsOnAllDatabases;

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'awardforce:remove-phantom-payment-methods';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manual migration to remove payment methods that belong to a non existing account.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->runOnAllDatabases(function () {
            $count = PaymentMethod::query()
                ->withTrashed()
                ->doesntHave('account')
                ->forceDelete();

            $this->info("{$count} orphaned payment methods destroyed.");
        });
    }
}
