<?php

namespace AwardForce\Console\Commands\Migrations;

use AwardForce\Console\Commands\RunsOnAllDatabases;
use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Grants\Commands\SeedDefaultGrantStatuses;
use AwardForce\Modules\Grants\Repositories\GrantStatusRepository;
use Illuminate\Console\Command;
use Illuminate\Foundation\Bus\DispatchesJobs;

class SeedDefaultGrantStatus extends Command
{
    use DispatchesJobs;
    use RunsOnAllDatabases;

    protected $signature = 'migrate:seed-default-grant-statuses';
    protected $description = 'Seeds default grant statuses to Good grants accounts';

    /** @var AccountRepository */
    private $accounts;

    /** @var AccountRepository */
    private $grantStatuses;

    public function __construct(AccountRepository $accounts, GrantStatusRepository $grantStatuses)
    {
        $this->accounts = $accounts;
        $this->grantStatuses = $grantStatuses;
        parent::__construct();
    }

    public function handle()
    {
        $this->runOnAllDatabases(function () {
            $this->accounts->getBy('brand', 'goodgrants')
                ->each(function (Account $account) {
                    if ($this->grantStatuses->countForAccount($account->id) === 0) {
                        $this->dispatch(new SeedDefaultGrantStatuses($account));
                    }
                });
        });
    }
}
