<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\Api\V2\EditApiKeyRequest;
use AwardForce\Http\Requests\Api\V2\GenerateApiKeyRequest;
use AwardForce\Modules\Api\V2\Commands\EditApiKey;
use AwardForce\Modules\Api\V2\Commands\GenerateApiKey;
use AwardForce\Modules\Api\V2\Commands\RevokeApiKey;
use AwardForce\Modules\Api\V2\Commands\ShowApiKey;
use AwardForce\Modules\Api\V2\Models\ApiKey;
use AwardForce\Modules\Api\V2\Search\ApiKeySearch;
use AwardForce\Modules\Api\V2\View\ApiKeyView;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Platform\Http\Controller;

class ApiKeyController extends Controller
{
    use DispatchesJobs;

    /** @var string */
    public static $resource = 'ApiKeys';

    /**
     * Show index page
     *
     * @return mixed
     */
    public function index(Request $request, ApiKeySearch $search)
    {
        $apiKeys = $search->fromInput($request->all());

        return $this->respond('api-key.index', compact('apiKeys'));
    }

    /**
     * Show generate form
     *
     * @return mixed
     */
    public function getGenerate(ApiKeyView $view)
    {
        return $this->respond('api-key.generate', $view);
    }

    /**
     * Show edit form
     *
     * @return mixed
     */
    public function edit(ApiKeyView $view)
    {
        return $this->respond('api-key.edit', $view);
    }

    /**
     * Create new API key
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function generate(GenerateApiKeyRequest $request)
    {
        $this->dispatch(new GenerateApiKey(current_account(), $request->name, $request->scope, $request->notificationEmails));

        return redirect()->route('api-key.index');
    }

    /**
     * Edit API key
     *
     * @return mixed
     */
    public function update(ApiKey $apiKey, EditApiKeyRequest $request)
    {
        $this->dispatch(new EditApiKey($apiKey, $request->name, $request->scope, $request->notificationEmails, $request->applicability));

        return redirect()->route('api-key.index');
    }

    /**
     * Revoke API key
     *
     * @return mixed
     */
    public function revoke(ApiKey $apiKey)
    {
        $this->dispatch(new RevokeApiKey($apiKey));

        return redirect()->route('api-key.index');
    }

    /**
     * Log that key was shown
     */
    public function show(ApiKey $apiKey)
    {
        $this->dispatch(new ShowApiKey($apiKey));

        return response()->noContent();
    }
}
