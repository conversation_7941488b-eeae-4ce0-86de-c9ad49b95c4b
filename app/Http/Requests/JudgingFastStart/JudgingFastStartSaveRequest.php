<?php

namespace AwardForce\Http\Requests\JudgingFastStart;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Judging\Validators\Wizard\IdsExist;
use AwardForce\Modules\Judging\Validators\Wizard\ScoreSet as ScoreSetValidator;
use AwardForce\Modules\Judging\Validators\Wizard\ScoreSetSeason;
use AwardForce\Modules\Tags\Contracts\TagRepository;
use Platform\Database\Repository;

class JudgingFastStartSaveRequest extends FormRequest
{
    public function rules()
    {
        return collect()
            ->merge($this->scoreSetRules())
            ->merge($this->scoringCriteriaRules())
            ->merge($this->panelRules())
            ->merge($this->roundRules())
            ->toArray();
    }

    protected function scoreSetRules()
    {
        return [
            'scoreSets' => ['required', 'array', new ScoreSetValidator, new ScoreSetSeason],
        ];
    }

    protected function scoringCriteriaRules()
    {
        return [
            'scoringCriteria' => ['array'],
            'scoringCriteria.*.maximumScore' => 'required|numeric',
            'scoringCriteria.*.minimumScore' => 'required|numeric',
            'scoringCriteria.*.scoringIncrement' => 'required|numeric|max_decimal_places:2|greater_than:0',
            'scoringCriteria.*.weight' => 'required|numeric',
        ];
    }

    protected function panelRules()
    {
        return [
            'panels' => ['array'],
            'panels.*.addMyself' => [
                'boolean',
            ],
            'panels.*.categoryIds' => [
                'sometimes',
                'array',
            ],
            'panels.*.chapterIds' => [
                'sometimes',
                'array',
                new IdsExist($this->availableIds(app(ChapterRepository::class)), trans('validation.judging_fast_start.panels.chapter_id')),
            ],
            'panels.*.tagIds' => [
                'sometimes',
                'array',
                new IdsExist($this->availableIds(app(TagRepository::class)), trans('validation.judging_fast_start.panels.tag_id')),
            ],
            'panels.*.roleIds' => [
                'sometimes',
                'array',
                new IdsExist($this->availableIds(app(RoleRepository::class)), trans('validation.judging_fast_start.panels.role_id')),
            ],
            'panels.*.emails' => ['array'],
            'panels.*.emails.*' => [
                'sometimes',
                'email',
            ],
            'panels.*.emailsRoleIds' => [
                'sometimes',
                'array',
                new IdsExist($this->availableIds(app(RoleRepository::class)), trans('validation.judging_fast_start.panels.role_id')),
            ],
        ];
    }

    protected function roundRules()
    {
        return [
            'rounds' => ['array'],
            'rounds.*.slug' => ['sometimes'],
            'rounds.*.startsAt' => ['nullable', 'date_format:Y-m-d H:i'],
            'rounds.*.startsTz' => ['required_with:startsAt'],
            'rounds.*.endsAt' => ['nullable', 'date_format:Y-m-d H:i'],
            'rounds.*.endsTz' => ['required_with:endsAt'],
        ];
    }

    protected function availableIds(Repository $repository): array
    {
        return $repository->getIds();
    }
}
