<?php

namespace AwardForce\Http\Requests\Discount;

class UpdateDiscountRequest extends BaseDiscountRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = parent::rules();

        $rules['code'] = 'required|alpha_num|unique:discounts,code,'.$this->discount->id.',id,season_id,'.$this->discount->seasonId;

        return $rules;
    }
}
