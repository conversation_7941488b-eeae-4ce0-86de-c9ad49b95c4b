<?php

namespace AwardForce\Http\Requests\AllocationPayments;

use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;

class UpdateAllocationPaymentRequest extends AllocationPaymentRequest
{
    public function rules(): array
    {
        /** @var AllocationPayment $allocationPayment */
        $allocationPayment = $this->route('allocationPayment');

        return array_merge(
            $this->getBaseRules(),
            [
                'amount' => [
                    'required',
                    'numeric',
                    'max:'.$this->allocation->getUnscheduledBalanceAmount()->add($allocationPayment->amount)->value(), ],
            ]
        );
    }
}
