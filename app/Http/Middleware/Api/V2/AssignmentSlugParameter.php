<?php

namespace AwardForce\Http\Middleware\Api\V2;

use AwardForce\Modules\Assignments\Models\AssignmentSlug;
use Closure;
use Dingo\Api\Routing\Helpers;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AssignmentSlugParameter
{
    use Helpers;

    public function handle(Request $request, Closure $next): Response
    {
        $slug = $request->route('slug');

        if (! AssignmentSlug::validate($slug)) {
            $this->response()->errorBadRequest("Invalid slug [{$slug}] in path.");
        }

        return $next($request);
    }
}
