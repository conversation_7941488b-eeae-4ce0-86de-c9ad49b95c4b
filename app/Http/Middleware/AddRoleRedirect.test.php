<?php

namespace AwardForce\Http\Middleware;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Mockery\MockInterface;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class AddRoleRedirectTest extends BaseTestCase
{
    use Laravel;

    private AddRoleRedirect $middleware;
    private Manager $manager;
    private MockInterface|RoleRepository $roles;
    private Role $registrationEnabledRole;
    private Role $registrationDisabledRole;

    public function init()
    {
        //by default user is not a guest, and roles registration is enabled
        $this->registrationEnabledRole = new Role;
        $this->registrationEnabledRole->registration = true;
        $this->registrationDisabledRole = new Role;
        $this->registrationDisabledRole->registration = false;

        $this->manager = $this->mock(Manager::class);
        $this->roles = $this->mock(RoleRepository::class);

        app()->instance(Manager::class, $this->manager);
        app()->instance(RoleRepository::class, $this->roles);

        $this->middleware = app(AddRoleRedirect::class);
    }

    public function testItShouldRedirectToAddRoleWhenUserIsNotAGuestAndARoleIsPresent()
    {
        $this->manager->shouldReceive('isGuest')->andReturnFalse();
        $this->roles->shouldReceive('getBySlug')->andReturn($this->registrationEnabledRole)->once();
        $request = $this->mock(Request::class);
        $request->shouldReceive('route')->with('requestedRole')->andReturn($roleSlug = 'someRole');

        $result = $this->middleware->handle($request, fn() => 'next');

        $this->assertInstanceOf(RedirectResponse::class, $result);
        $this->assertEquals(route('add.role', ['role' => $roleSlug]), $result->getTargetUrl());
    }

    public function testItShouldNotRedirectToAddRoleWhenUserIsGuest()
    {
        $this->manager->shouldReceive('isGuest')->andReturnTrue();
        $this->roles->shouldNotReceive('getBySlug');
        $request = $this->mock(Request::class);
        $request->shouldReceive('route')->with('requestedRole')->andReturn('someRole');

        $result = $this->middleware->handle($request, fn() => 'next');

        $this->assertEquals('next', $result);
    }

    public function testItShouldNotRedirectToAddRoleWhenRoleIsNotPresent()
    {
        $this->manager->shouldReceive('isGuest')->andReturnFalse();
        $this->roles->shouldNotReceive('getBySlug');
        $request = $this->mock(Request::class);
        $request->shouldReceive('route')->with('requestedRole')->andReturnNull();

        $result = $this->middleware->handle($request, fn() => 'next');

        $this->assertEquals('next', $result);
    }

    public function testItShouldNotRedirectToAddRoleWhenRoleIsNotRegistrationEnabled()
    {
        $this->manager->shouldReceive('isGuest')->andReturnFalse();
        $this->roles->shouldReceive('getBySlug')->andReturn($this->registrationDisabledRole)->once();
        $request = $this->mock(Request::class);
        $request->shouldReceive('route')->with('requestedRole')->andReturn('someRole');

        $result = $this->middleware->handle($request, fn() => 'next');

        $this->assertEquals('next', $result);
    }
}
