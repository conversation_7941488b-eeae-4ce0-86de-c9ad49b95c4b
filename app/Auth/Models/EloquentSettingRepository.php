<?php

namespace AwardForce\Auth\Models;

class EloquentSettingRepository extends EloquentRepository implements SettingRepository
{
    public function __construct(Setting $model)
    {
        $this->model = $model;
    }

    /**
     * Retrieve a setting by its name.
     *
     * @param  mixed  $default
     * @return mixed
     */
    public function getValueByKey(string $account, string $key, $default = null)
    {
        $setting = $this->model->newQuery()
            ->join('accounts', 'accounts.id', '=', 'settings.account_id')
            ->where('accounts.slug', '=', $account)
            ->where('key', $key)
            ->first();

        return $setting ? $setting->value : $default;
    }

    /**
     * Returns a collection of all records for this repository and the models or entities it represents.
     *
     * @return Collection
     */
    public function getAll(string $accountSlug)
    {
        $accountId = Account::whereSlug($accountSlug)->firstOrFail()->id;

        return Setting::where('account_id', '=', $accountId)
            ->get();
    }

    /**
     * Save setting with updated value.
     *
     * @return void
     *
     * @throws \Exception
     */
    public function saveSetting(string $accountSlug, string $settingKey, string $settingValue)
    {
        if (is_null($settingValue)) {
            $this->removeByKey($accountSlug, $settingKey);

            return;
        }

        $accountId = Account::whereSlug($accountSlug)->firstOrFail()->id;

        $setting = Setting::firstOrNew(['account_id' => $accountId, 'key' => $settingKey]);
        $setting->value = $settingValue;
        $this->save($setting);
    }

    /**
     * Removes (forgets) setting by account slug and key name.
     *
     * @return mixed|void
     *
     * @throws \Exception
     */
    public function removeByKey(string $accountSlug, string $key)
    {
        $accountId = Account::whereSlug($accountSlug)->firstOrFail()->id;

        return Setting::where('account_id', $accountId)
            ->where('key', $key)
            ->delete();
    }
}
