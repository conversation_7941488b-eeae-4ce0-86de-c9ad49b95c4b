<?php

namespace AwardForce\Library\Html;

use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\Identity\Roles\Services\PermissionsService;
use Platform\Html\Parsedown;
use Platform\Html\Permissions;

class HtmlServiceProvider extends ServiceProvider
{
    protected $files = [
        __DIR__.'/helpers.php',
        __DIR__.'/links.php',
        __DIR__.'/macros.php',
    ];

    public function register()
    {
        parent::register();

        $this->registerMultilingualFormBuilder();
        $this->registerPermissions();
        $this->registerMarkdownCache();
    }

    private function registerMultilingualFormBuilder()
    {
        $this->app->scoped('mlform', MultiLingualFormBuilder::class);
    }

    private function registerPermissions()
    {
        $this->app->singleton(Permissions::class, PermissionsService::class);
    }

    private function registerMarkdownCache()
    {
        $this->app->singleton(MarkdownCache::class, (function () {
            return new MarkdownCache((new Parsedown)->setCacheExpiry(MarkdownCache::CACHE_EXPIRY));
        }));
    }
}
