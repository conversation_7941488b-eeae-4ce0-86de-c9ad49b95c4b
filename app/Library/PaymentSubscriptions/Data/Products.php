<?php

namespace AwardForce\Library\PaymentSubscriptions\Data;

use Assert\Assertion;
use Illuminate\Support\Collection;

class Products extends Collection
{
    public function __construct(array $products = [])
    {
        Assertion::allIsInstanceOf($products, Product::class);

        parent::__construct($products);
    }

    public static function fromConfig(): self
    {
        $products = collect(config('products.available'))
            ->keys()
            ->map(fn($product) => new Product($product))
            ->toArray();

        return new static($products);
    }

    public function brand(string $brand): self
    {
        return $this->filter(fn(Product $product) => $product->brand() === $brand);
    }

    public function plans(): Plans
    {
        $plans = collect($this->items)
            ->map(fn(Product $product) => $product->plan())
            ->unique()
            ->values()
            ->toArray();

        return new Plans($plans);
    }
}
