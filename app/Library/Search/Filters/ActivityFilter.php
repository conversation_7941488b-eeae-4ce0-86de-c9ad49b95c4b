<?php

namespace AwardForce\Library\Search\Filters;

use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class ActivityFilter implements ColumnatorFilter, SearchFilter
{
    /** @var array */
    private $input;

    /** @var string */
    private $column;

    public function __construct(array $input, string $column)
    {
        $this->input = $input;
        $this->column = $column;
    }

    /**
     * Return true if this search filter applies to the search in any way.
     */
    public function applies(): bool
    {
        return isset($this->input['activity']) || isset($this->input['activityOrder']);
    }

    /**
     * {@inheritdoc}
     */
    public function applyToEloquent($query)
    {
        if (! empty($this->input['activity'])) {
            $query = $query->whereIn($this->column, $this->input['activity']);
        }

        if (isset($this->input['activityOrder'])) {
            $query = $query->orderByRaw($this->rawOrdering());
        }

        return $query;
    }

    protected function rawOrdering()
    {
        $this->input['activityOrder'] = array_reverse($this->input['activityOrder']);

        return 'FIELD('.$this->column.','.sprintf('"%s"', implode('", "', $this->input['activityOrder'])).') '.($this->input['dir'] ?? 'desc');
    }
}
