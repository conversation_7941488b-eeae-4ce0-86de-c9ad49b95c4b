<?php

namespace AwardF<PERSON>ce\Library\Broadcasting;

class EntryPusher extends Pusher
{
    /** @var string|null */
    private $entrySlug;

    public function __construct(?string $entrySlug = null)
    {
        $this->entrySlug = $entrySlug;
    }

    public function channels(): array
    {
        return array_filter(array_merge(parent::channels(), [$this->entryChannel()]));
    }

    public function entryChannel(): ?string
    {
        if ($this->entrySlug) {
            return 'private-entry-'.$this->entrySlug;
        }

        return null;
    }
}
