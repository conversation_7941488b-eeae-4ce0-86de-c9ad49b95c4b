<?php

namespace AwardForce\Library\Broadcasting;

use AwardForce\Library\Notifications\Slack;
use Illuminate\Broadcasting\Broadcasters\PusherBroadcaster;
use Illuminate\Broadcasting\BroadcastException;
use Illuminate\Foundation\Bus\DispatchesJobs;

class AfPusherBroadcaster extends PusherBroadcaster
{
    use DispatchesJobs;

    public function broadcast(array $channels, $event, array $payload = [])
    {
        try {
            parent::broadcast($channels, $event, $payload);
        } catch (BroadcastException $e) {
            \Log::notice($e->getMessage());
            app(Slack::class)->commandException($event, $e);
        }
    }
}
