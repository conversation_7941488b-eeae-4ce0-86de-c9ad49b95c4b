<?php

namespace AwardForce\Library\Cache;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;

trait Cacher
{
    /**
     * @var array
     */
    private static $cacherRequestData = [];

    /**
     * @var array
     */
    private $cacherInstanceData = [];

    public static function nukeCache()
    {
        self::$cacherRequestData = [];
    }

    /**
     * Caches the key<->value pair on the instance level
     *
     * @return mixed
     */
    protected function instanceCache(string $key, callable $value)
    {
        if (isset($this->cacherInstanceData[$key])) {
            return $this->cacherInstanceData[$key];
        }

        return $this->cacherInstanceData[$key] = call_user_func($value);
    }

    /**
     * Caches the key<->value pair on the request level, across instances
     *
     * @return mixed
     */
    protected function requestCache(string $key, callable $value)
    {
        if (! isset(self::$cacherRequestData[$class = get_class($this)])) {
            self::$cacherRequestData[$class] = [];
        }

        if (isset(self::$cacherRequestData[$class][$key])) {
            return self::$cacherRequestData[$class][$key];
        }

        return self::$cacherRequestData[$class][$key] = call_user_func($value);
    }

    /**
     * Caches the key<->value pair on the session level, across requests and instances
     *
     * @return mixed
     */
    protected function sessionCache(string $key, callable $value)
    {
        if (Session::has($key = md5(get_class($this).':'.$key))) {
            return Session::get($key);
        }

        Session::put($key, $value = call_user_func($value));

        return $value;
    }

    /**
     * Caches the key<->value pair on the global level
     *
     * @return mixed
     */
    protected function globalCache(string $key, string $expiryKey, callable $value)
    {
        $expiry = config('cache.expiry.'.$expiryKey, config('cache.expiry.default'));

        return Cache::remember(get_class($this).':'.$key, now()->addMinutes($expiry), $value);
    }

    protected function globalFlexibleCache(string $key, string $expiryKey, callable $value)
    {
        $fresh = config('cache.fresh.'.$expiryKey, config('cache.fresh.default'));
        $stale = config('cache.stale.'.$expiryKey, config('cache.stale.default'));
        $key = get_class($this).':'.current_account_id().'-'.$key;

        return Cache::flexible($key, [$fresh, $stale], $value);
    }
}
