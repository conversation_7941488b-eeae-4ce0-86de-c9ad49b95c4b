name: "Update Changelog"

on:
  workflow_dispatch:
  workflow_run:
    workflows: ["Publish Draft Release"]
    types:
      - completed
  release:
    types:
      - released

jobs:
  update:
    name: "Update Changelog"
    if: ${{ github.event_name != 'workflow_run' || github.event.workflow_run.conclusion == 'success' }}
    uses: tectonic/.github/.github/workflows/update-changelog.yml@master
    secrets:
      GIT_TECHDEPLOY_TOKEN: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
